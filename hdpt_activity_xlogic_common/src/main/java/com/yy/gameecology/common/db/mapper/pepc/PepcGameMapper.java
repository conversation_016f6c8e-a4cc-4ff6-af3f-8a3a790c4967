package com.yy.gameecology.common.db.mapper.pepc;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGame;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface PepcGameMapper {
    @Update("""   
            <script>
                          update  pepc_game set state= #{newStatus}
                                <if test="siteId != null">
                                      , site_id = #{siteId}
                                    </if>
                                    <if test="parentId != null">
                                      , parent_id = #{parentId}
                                    </if>
                                    <if test="matchId != null">
                                      , match_id = #{matchId}
                                    </if>
                          where
                          id = #{id}
                          and act_id= #{actId}
                          and state=  #{oldStatus}
                            </script>
                        """)
    int updateGameState(@Param("actId") long actId,
                        @Param("id") long id,
                        @Param("newStatus") int newStatus,
                        @Param("oldStatus") int oldStatus,
                        @Param("siteId") Long siteId, @Param("parentId") Long parentId, @Param("matchId") Long matchId);


    @Select("""
            select * from pepc_game 
            where act_id = #{actId} and start_time  between #{startTimeBegin} and #{startTimeEnd}
                 """)
    List<PepcGame> selectPepcGame(@Param("actId") Long actId, @Param("startTimeBegin") Date startTimeBegin, @Param("startTimeEnd") Date startTimeEnd);

    @Select("""
              <script>
             select * from pepc_game 
             where act_id= #{actId} 
            and id in  
            <foreach item='item' index='index' collection='gameIds' open='(' separator=',' close=')'>
                        #{item}
             </foreach>
     
            order by start_time asc
             </script>
                  """)
    List<PepcGame> selectPepcGameByGameIds(@Param("actId") Long actId,@Param("gameIds") List<Long> gameIds);

}
