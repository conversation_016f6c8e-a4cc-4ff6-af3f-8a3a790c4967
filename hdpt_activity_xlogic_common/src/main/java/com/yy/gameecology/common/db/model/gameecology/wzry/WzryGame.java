package com.yy.gameecology.common.db.model.gameecology.wzry;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 15:09
 **/
@Data
@TableColumn(underline = true)
public class WzryGame implements Serializable {

    public static String TABLE_NAME = "wzry_game";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<WzryGame> ROW_MAPPER = null;
    /**
     * id
     */
    private Long id;

    private Long actId;

    private String gameViewCode;

    /**
     * 赛事编码
     */
    private String gameCode;

    /**
     * 赛事名称
     */
    private String gameName;

    /**
     * 赛事类型 0=5V5 1=1V1 3=3V3
     */
    private Integer battleMode;

    /**
     * 王者赛宝-赛事id
     */
    private String childid;

    /**
     * 已参赛人数
     */
    private Integer enters;

    /**
     * 赛事状态 0-初始状态 100-赛宝未上场异常取消 200-游戏未上场异常取消 900-正常结束
     */
    private Integer state;

    /**
     * 比赛胜利的队伍 0-初始状态 1-胜利的是A战队 2-胜利的是B战队
     */
    private Integer winnerTeam;

    /**
     * 如果得比赛每个队员能获得的赏金
     */
    private Long award;

    /**
     * 报名开始时间
     */
    private Date signUpStartTime;

    /**
     * 报名截止时间
     */
    private Date signUpEndTime;

    /**
     * 比赛开启时间
     */
    private Date startTime;

    /**
     * 比赛结算时间
     */
    private Date endTime;

    /**
     * ext_date
     */
    private String extDate;

    /**
     * 赛宝结果
     */
    private String gameResultData;

    /**
     * 最后一次检查游戏结束时间，初始化的时候和创建时间一致
     */
    private Date lastCheckTime;

    /**
     * 王者赛宝房间满人时间
     */
    private Date saiBaoRoomFullTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public WzryGame() {}
}
