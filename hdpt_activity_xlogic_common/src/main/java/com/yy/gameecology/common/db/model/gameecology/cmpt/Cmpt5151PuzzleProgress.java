package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 星河之约拼图进度表
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
@TableColumn(underline = true)
public class Cmpt5151PuzzleProgress implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5151_puzzle_progress";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5151PuzzleProgress> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5151PuzzleProgress result = new Cmpt5151PuzzleProgress();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setStageNo(rs.getInt("stage_no"));
        result.setPuzzlePosition(rs.getInt("puzzle_position"));
        result.setIsUnlocked(rs.getInt("is_unlocked"));
        result.setUnlockTime(rs.getTimestamp("unlock_time"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主持UID
     */
    private Long anchorUid;

    /**
     * 阶段编号(1-3)
     */
    private Integer stageNo;

    /**
     * 拼图位置(1-9)
     */
    private Integer puzzlePosition;

    /**
     * 是否已解锁(0-未解锁，1-已解锁)
     */
    private Integer isUnlocked;

    /**
     * 解锁时间
     */
    private Date unlockTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
