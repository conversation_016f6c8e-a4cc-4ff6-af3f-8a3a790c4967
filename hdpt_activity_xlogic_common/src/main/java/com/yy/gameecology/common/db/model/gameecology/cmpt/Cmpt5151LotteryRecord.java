package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 星河之约口令抽奖记录表
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
@TableColumn(underline = true)
public class Cmpt5151LotteryRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5151_lottery_record";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5151LotteryRecord> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5151LotteryRecord result = new Cmpt5151LotteryRecord();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setChannelId(rs.getString("channel_id"));
        result.setDateCode(rs.getString("date_code"));
        result.setRewardName(rs.getString("reward_name"));
        result.setRewardAmount(rs.getInt("reward_amount"));
        result.setIsWin(rs.getInt("is_win"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 频道ID
     */
    private String channelId;

    /**
     * 日期编码(yyyyMMdd)
     */
    private String dateCode;

    /**
     * 奖励名称
     */
    private String rewardName;

    /**
     * 奖励数量
     */
    private Integer rewardAmount;

    /**
     * 是否中奖(0-未中奖，1-中奖)
     */
    private Integer isWin;

    /**
     * 创建时间
     */
    private Date createTime;
}
