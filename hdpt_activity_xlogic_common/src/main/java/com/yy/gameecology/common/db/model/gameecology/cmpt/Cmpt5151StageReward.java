package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 星河之约阶段奖励记录表
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
@TableColumn(underline = true)
public class Cmpt5151StageReward implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5151_stage_reward";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5151StageReward> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5151StageReward result = new Cmpt5151StageReward();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setStageNo(rs.getInt("stage_no"));
        result.setRewardType(rs.getInt("reward_type"));
        result.setRewardIndex(rs.getInt("reward_index"));
        result.setRewardName(rs.getString("reward_name"));
        result.setIsReceived(rs.getInt("is_received"));
        result.setReceiveTime(rs.getTimestamp("receive_time"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主持UID
     */
    private Long anchorUid;

    /**
     * 阶段编号(1-3)
     */
    private Integer stageNo;

    /**
     * 奖励类型(1-横向连线，2-竖向连线，3-全碎片)
     */
    private Integer rewardType;

    /**
     * 奖励索引(连线编号1-3，全碎片为0)
     */
    private Integer rewardIndex;

    /**
     * 奖励名称
     */
    private String rewardName;

    /**
     * 是否已领取(0-未领取，1-已领取)
     */
    private Integer isReceived;

    /**
     * 领取时间
     */
    private Date receiveTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
