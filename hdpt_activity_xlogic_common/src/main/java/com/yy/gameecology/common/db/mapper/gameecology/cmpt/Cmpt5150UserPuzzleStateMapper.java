package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5150UserPuzzleState;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface Cmpt5150UserPuzzleStateMapper {


    @Update("""
                INSERT INTO cmpt_5150_user_puzzle_state (act_id, cmpt_use_inx, user_uid, anchor_uid, round, create_time, update_time)
                VALUES (#{actId}, #{cmptUseInx}, #{userUid}, #{anchorUid}, #{addRound}, NOW(), NOW())
                ON DUPLICATE KEY UPDATE round = round + #{addRound}, update_time = VALUES(update_time)
            """)
    int insertOrUpdateUserPuzzleState(
            @Param("actId") Long actId,
            @Param("cmptUseInx") Long cmptUseInx,
            @Param("userUid") Long userUid,
            @Param("anchorUid") Long anchorUid,
            @Param("addRound") Integer addRound);

    @Select("""
                select * from cmpt_5150_user_puzzle_state 
                WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} AND user_uid = #{userUid} and anchor_uid = #{anchorUid}
            """)
    Cmpt5150UserPuzzleState select(@Param("actId") Long actId, @Param("cmptUseInx") Long cmptUseInx, @Param("userUid") Long userUid, @Param("anchorUid") Long anchorUid);


}
