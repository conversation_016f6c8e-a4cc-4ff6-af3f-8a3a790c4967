package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 星河之约奖池余额表
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
@TableColumn(underline = true)
public class Cmpt5151GiftPool implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5151_gift_pool";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5151GiftPool> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5151GiftPool result = new Cmpt5151GiftPool();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setTotalAmount(rs.getLong("total_amount"));
        result.setUsedAmount(rs.getLong("used_amount"));
        result.setRemainingAmount(rs.getLong("remaining_amount"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 奖池总金额(单位：分)
     */
    private Long totalAmount;

    /**
     * 已使用金额(单位：分)
     */
    private Long usedAmount;

    /**
     * 剩余金额(单位：分)
     */
    private Long remainingAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
