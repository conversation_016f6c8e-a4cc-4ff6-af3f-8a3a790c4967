package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-02-05 16:21
 **/
@Data
@TableColumn(underline = true)
public class PepcGameLive implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_game_live";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcGameLive> ROW_MAPPER = null;

    private Long id;

    private Long actId;

    private Integer phaseId;

    private String groupCode;

    /**
     * game_id
     */
    private Long gameId;


    /**
     * site_id
     */
    private Long siteId;

    /**
     * parent_id
     */
    private Long parentId;

    /**
     * match_id
     */
    private Long matchId;

    /**
     * join_func
     */
    private Integer joinFunc;

    /**
     * module_id
     */
    private Integer moduleId;

    /**
     * start_time
     */
    private Date startTime;

    /**
     * 1--直播中 2--直播回放
     */
    private Integer state;

    /**
     * 直播数据流地址
     */
    private String gameLiveInfo;

    /**
     * game_record_info
     */
    private String gameRecordInfo;

    /**
     * create_time
     */
    private Date createTime;
}
