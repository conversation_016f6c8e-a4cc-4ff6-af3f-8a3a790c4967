package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-13 11:36
 **/
@Data
@TableColumn(underline = true)
public class Cmpt1017DayTaskItemState implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_1017_day_task_item_state_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt1017DayTaskItemState> ROW_MAPPER = null;

    private Long id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * 组件索引
     */
    private Long cmptUseInx;

    /**
     * member
     */
    private String member;

    /**
     * 子任务项目
     */
    private String item;

    /**
     * 完成任务天
     */
    private Integer dayIndex;

    /**
     * value
     */
    private Long value;

    private Date upateTime;

    /**
     * create_time
     */
    private Date createTime;
}
