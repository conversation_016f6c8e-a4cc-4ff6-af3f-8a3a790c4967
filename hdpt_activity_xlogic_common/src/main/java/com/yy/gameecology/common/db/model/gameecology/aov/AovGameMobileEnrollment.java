package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovGameMobileEnrollment {

    /**
     *
     */
    private Long uid;


    private Long actTp;

    /**
     *
     */
    private String mobileHash;

    /**
     * 首次报名时间
     */
    private Date createTime;

    public AovGameMobileEnrollment() {
    }

    public AovGameMobileEnrollment(Long uid, Long actTp, String mobileHash, Date createTime) {
        this.uid = uid;
        this.actTp = actTp;
        this.mobileHash = mobileHash;
        this.createTime = createTime;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getMobileHash() {
        return mobileHash;
    }

    public void setMobileHash(String mobileHash) {
        this.mobileHash = mobileHash == null ? null : mobileHash.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getActTp() {
        return actTp;
    }

    public void setActTp(Long actTp) {
        this.actTp = actTp;
    }
}
