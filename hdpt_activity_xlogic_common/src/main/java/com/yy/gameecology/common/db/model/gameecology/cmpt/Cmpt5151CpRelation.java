package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 星河之约CP关系表
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
@TableColumn(underline = true)
public class Cmpt5151CpRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5151_cp_relation";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5151CpRelation> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5151CpRelation result = new Cmpt5151CpRelation();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setDateCode(rs.getString("date_code"));
        result.setTotalCpValue(rs.getLong("total_cp_value"));
        result.setDailyCpValue(rs.getLong("daily_cp_value"));
        result.setLastGiftTime(rs.getTimestamp("last_gift_time"));
        result.setLastChannelId(rs.getString("last_channel_id"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主持UID
     */
    private Long anchorUid;

    /**
     * 日期编码(yyyyMMdd)
     */
    private String dateCode;

    /**
     * 总CP值
     */
    private Long totalCpValue;

    /**
     * 当日CP值
     */
    private Long dailyCpValue;

    /**
     * 最后送礼时间
     */
    private Date lastGiftTime;

    /**
     * 最后送礼频道ID
     */
    private String lastChannelId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
