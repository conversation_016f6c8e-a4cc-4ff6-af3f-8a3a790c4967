package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5151CpRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 星河之约CP关系表Mapper
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Mapper
public interface Cmpt5151CpRelationMapper {

    /**
     * 插入或更新CP关系
     */
    int insertOrUpdateCpRelation(@Param("actId") Long actId,
                                @Param("cmptUseInx") Integer cmptUseInx,
                                @Param("userUid") Long userUid,
                                @Param("anchorUid") Long anchorUid,
                                @Param("dateCode") String dateCode,
                                @Param("cpValue") Long cpValue,
                                @Param("channelId") String channelId);

    /**
     * 获取CP关系
     */
    Cmpt5151CpRelation getCpRelation(@Param("actId") Long actId,
                                    @Param("cmptUseInx") Integer cmptUseInx,
                                    @Param("userUid") Long userUid,
                                    @Param("anchorUid") Long anchorUid,
                                    @Param("dateCode") String dateCode);

    /**
     * 获取用户的所有CP关系（按当日CP值降序）
     */
    List<Cmpt5151CpRelation> getUserCpRelations(@Param("actId") Long actId,
                                               @Param("cmptUseInx") Integer cmptUseInx,
                                               @Param("userUid") Long userUid,
                                               @Param("dateCode") String dateCode);

    /**
     * 获取主持的所有CP关系（按当日CP值降序）
     */
    List<Cmpt5151CpRelation> getAnchorCpRelations(@Param("actId") Long actId,
                                                 @Param("cmptUseInx") Integer cmptUseInx,
                                                 @Param("anchorUid") Long anchorUid,
                                                 @Param("dateCode") String dateCode);

    /**
     * 获取用户当日最高CP值的CP关系
     */
    Cmpt5151CpRelation getUserTopCpRelation(@Param("actId") Long actId,
                                           @Param("cmptUseInx") Integer cmptUseInx,
                                           @Param("userUid") Long userUid,
                                           @Param("dateCode") String dateCode);

    /**
     * 获取主持当日最高CP值的CP关系
     */
    Cmpt5151CpRelation getAnchorTopCpRelation(@Param("actId") Long actId,
                                             @Param("cmptUseInx") Integer cmptUseInx,
                                             @Param("anchorUid") Long anchorUid,
                                             @Param("dateCode") String dateCode);

    /**
     * 更新CP值
     */
    int updateCpValue(@Param("actId") Long actId,
                     @Param("cmptUseInx") Integer cmptUseInx,
                     @Param("userUid") Long userUid,
                     @Param("anchorUid") Long anchorUid,
                     @Param("dateCode") String dateCode,
                     @Param("addCpValue") Long addCpValue,
                     @Param("channelId") String channelId);

    /**
     * 重置每日CP值
     */
    int resetDailyCpValue(@Param("actId") Long actId,
                         @Param("cmptUseInx") Integer cmptUseInx,
                         @Param("dateCode") String dateCode);
}
