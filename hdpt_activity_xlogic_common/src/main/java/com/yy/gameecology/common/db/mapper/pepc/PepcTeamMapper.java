package com.yy.gameecology.common.db.mapper.pepc;

import com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface PepcTeamMapper {
    int insertAndGetId(PepcTeam record);

    PepcTeam selectById(@Param("id") long id);

    List<PepcTeam> selectByIds(@Param("teamIds") List<Long> teamIds);

    PepcTeam selectPepcTeam(@Param("uid") long uid, @Param("actId") long actId);

    List<PepcTeam> selectInitTeam(@Param("actId") long actId, @Param("cnt") long cnt, @Param("state") int state);

    List<PepcTeam> listTeam(@Param("actId") long actId, @Param("cnt") long cnt);

    List<PepcTeam>  pageList(@Param("actId") long actId, @Param("offset") int offset, @Param("pageSize") int pageSize);

//    int incrTeamCnt(@Param("id") long id, @Param("score") long score);
//
//    int decrTeamCnt(@Param("id") long id, @Param("score") long score);

    int incrTeamCnt1(@Param("id") long id);

    int decrTeamCnt1(@Param("id") long id);

    int invalidTeam(@Param("actId") long actId, @Param("cnt") long cnt, @Param("state") int state);

    List<PepcTeam> selectValidTeamIds(@Param("actId") long actId);

    int updateState(@Param("ids") List<Long> ids, @Param("state") int state);

    int updateDeclaration(@Param("declarationAudit") String declarationAudit, @Param("auditState") int auditState, @Param("id") long id);

    int acceptDeclaration( @Param("auditState") int auditState, @Param("id") long id, @Param("oldState") int oldState);

    int clearAuditDeclaration( @Param("auditState") int auditState, @Param("id") long id, @Param("oldState") int oldState);

    PepcTeam selectTeamBySid(@Param("actId") long actId, @Param("sid") long sid, @Param("ssid") long ssid);

    int countTeam(@Param("actId") long actId, @Param("state") Integer state, @Param("floorMemberCnt") Integer floorMemberCnt);

    int turnNeedApply(@Param("teamId") long teamId);

    int deleteTeam(@Param("teamId") long teamId);

    @Update("""
    update  pepc_team set team_name = #{teamName} where id = #{id}
    """)
    int updateTeamName(@Param("id") long id, @Param("teamName") String teamName);

    @Update("""
    update  pepc_team set team_name_audit = #{teamNameAudit} where id = #{id}
    """)
    int updateTeamNameAudit(@Param("id") long id, @Param("teamNameAudit") String teamNameAudit);

    @Update("""
    update  pepc_team set team_name_audit_state = #{status} where id = #{id}
    """)
    int updateTeamNameAuditStatue(@Param("id") long id, @Param("status") int status);

    @Update("""
            <script>
                    UPDATE pepc_team 
                    SET state = #{state} 
                    WHERE act_id = #{actId} 
                    AND id IN 
                    <foreach item='item' index='index' collection='teamId' open='(' separator=',' close=')'>
                        #{item}
                    </foreach>
                    </script>
            """)
    int updateTeamState(@Param("actId") long actId, @Param("teamId") List<Long> teamId, @Param("state") int state);
}
