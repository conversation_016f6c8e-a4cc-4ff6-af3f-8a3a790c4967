package com.yy.gameecology.common.db.mapper.aov;

import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseSubscribe;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AovPhaseSubscribeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AovPhaseSubscribe record);

    int insertSelective(AovPhaseSubscribe record);

    AovPhaseSubscribe selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AovPhaseSubscribe record);

    int updateByPrimaryKey(AovPhaseSubscribe record);

    AovPhaseSubscribe selectUserSubscribe(@Param("actId") long actId, @Param("phaseId") long phaseId, @Param("uid") long uid);

    int updateSubscribeState(@Param("id") long id, @Param("sourceState") Integer sourceState, @Param("targetState") int targetState);

    List<AovPhaseSubscribe> selectSubscribes(@Param("actId") long actId, @Param("phaseId") long phaseId, @Param("state") Integer state, @Param("size") int size);

    int countSubscribes(@Param("actId") long actId, @Param("phaseId") long phaseId);
}