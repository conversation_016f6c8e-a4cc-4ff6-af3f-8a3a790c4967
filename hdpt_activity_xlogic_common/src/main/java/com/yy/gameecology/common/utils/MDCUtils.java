package com.yy.gameecology.common.utils;

import org.slf4j.MDC;

/**
 * <AUTHOR> 2021/3/13
 */
public class MDCUtils {

    public static final void putContext(String origin) {
        putContext(origin, null);
    }

    public static final void putContext(String origin, String seq) {
        if (seq == null || seq.isEmpty()) {
            seq = String.valueOf(TraceId.nextId());
        }
        MDC.put("origin", origin);
        MDC.put("trace", "traceId=" + seq);
    }

    public static void clearContext() {
        MDC.clear();
    }
}
