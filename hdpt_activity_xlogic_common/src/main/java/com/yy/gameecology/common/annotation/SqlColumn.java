package com.yy.gameecology.common.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

@Retention(RetentionPolicy.RUNTIME)
@Target(value = {FIELD})
public @interface SqlColumn {

    /**
     * 列名
     */
    String value() default "";

    /**
     * 是否使用下划线
     */
    boolean underline() default false;
}
