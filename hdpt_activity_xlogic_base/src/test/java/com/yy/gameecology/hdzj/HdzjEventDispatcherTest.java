package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-1.properties"})
public class HdzjEventDispatcherTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @Test
    public void doNotify() {
        log.info("doNotify HdztAwardLotteryMsg start .......................");
        HdztAwardLotteryMsg hdztAwardLotteryMsg = new HdztAwardLotteryMsg();
        hdztAwardLotteryMsg.setActId(1);
        hdzjEventDispatcher.notify(hdztAwardLotteryMsg);

//        log.info("doNotify ActivityTimeStart start >>>>>>>>>>>>>>>>>>>>>>>>>>");
//        ActivityTimeStart baseEvent = new ActivityTimeStart();
//        baseEvent.setActId(99999);
//        hdzjEventDispatcher.notify(baseEvent);

        log.info("doNotify done!");
    }
}
