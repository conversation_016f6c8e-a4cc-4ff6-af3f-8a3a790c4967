package com.yy.gameecology.activity.service.pepc;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.mapper.pepc.PepcPhaseInfoMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamGroupMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.bean.pepc.*;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-01 15:09
 **/
@Service
public class PepcTeamGroupService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Lazy
    @Autowired
    private PepcTeamGroupService myself;

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PepcTeamMapper pepcTeamMapper;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private PepcPhaseInfoMapper pepcPhaseInfoMapper;

    @Autowired
    private PepcTeamGroupMapper pepcTeamGroupMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    private static final String TEAM_GROUP_END_NOTIFY = """
            满足   ${teamMinMember} 人参赛队数量：${teamAmount}
            不满足 ${teamMinMember} 人参赛队数量：${notEnoughTeamAmount}
            最小开赛队伍数量配置：${startGameMinTeam}
            赛事是否成功开启：${startGame}
            """;

    /**
     * 报名结束后初始化分组数据
     */
    public void doTeamGrouping(Date now, PepcPhaseComponentAttr attr, long actId) {
        Date groupTime = DateUtil.addMinutes(attr.getSignupEndTime(), attr.getSignupEndDelayGroupMin());
        if (now.before(groupTime)) {
            //分组时间没到
            return;
        }

        if (isGroupComplete(attr)) {
            return;
        }

        //前置阶段数据未初始化完成
        long phaseCount = pepcDao.countPepcPhaseInfo(actId);
        if (phaseCount <= 0) {
            return;
        }

        log.info("doTeamGrouping actId:{}", actId);
        List<PepcTeam> teams = pepcDao.getAllTeam(actId);
        long effectTeamCnt = teams.stream().filter(p -> p.getMemberCnt() >= attr.getTeamMinMember()).count();
        if (effectTeamCnt < attr.getStartGameMinTeam()) {
            //不满足开赛最小队伍
            log.info("teamCnt not enough,actId:{}", actId);
            myself.saveCancelGameGroupData(attr, teams);
        } else {
            myself.saveStartGameGroupData(attr, teams);
        }

        //如流通知
//        String content = commonService.renderTemplate(TEAM_GROUP_END_NOTIFY,
//                ImmutableMap.of("teamMinMember", attr.getTeamMinMember(),
//                        "teamAmount", effectTeamCnt,
//                        "notEnoughTeamAmount", teams.size() - effectTeamCnt,
//                        "startGameMinTeam", attr.getStartGameMinTeam(),
//                        "startGame", effectTeamCnt >= attr.getStartGameMinTeam()));
//        String msg = commonService.buildActRuliuMsg(actId, false, "和平精英巅峰赛分组完成", content);
//        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, msg, Collections.emptyList());
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveStartGameGroupData(PepcPhaseComponentAttr attr, List<PepcTeam> teams) {
        MakeTeamGroupResult group = makeTeamGroup(attr, teams);

        List<Long> realJoin = group.getRealJoinGameGroups().stream().map(PepcTeamGroup::getTeamId).toList();
        List<Long> notEnouth = group.getNotEnoughMemberTeams().stream().map(PepcTeam::getId).toList();
        List<Long> overlimit = group.getOverLimitTeams().stream().map(PepcTeam::getId).toList();

        //完成保存状态
        boolean saveTag = saveActGroupState(attr, PepcConst.ActState.GROUP_COMPLETED);
        if (!saveTag) {
            log.warn("already saveGroupData,actId:{}", attr.getActId());
            return;
        }

        pepcDao.saveTeamGroup(group.getRealJoinGameGroups());

        pepcTeamMapper.updateTeamState(attr.getActId(), realJoin, PepcConst.PhaseTeamState.SUCC);

        if (CollectionUtils.isNotEmpty(notEnouth)) {
            pepcTeamMapper.updateTeamState(attr.getActId(), notEnouth, PepcConst.PhaseTeamState.NO_ENOUGH);
        }
        if (CollectionUtils.isNotEmpty(overlimit)) {
            pepcTeamMapper.updateTeamState(attr.getActId(), overlimit, PepcConst.PhaseTeamState.OVER_LIMIT);
        }

        //标记可以初始化游戏数据
        pepcPhaseInfoMapper.updatePepcPhaseInfoStatus(attr.getActId(), attr.getFirstPhaseIdExcludeSignUp(), PepcConst.PhaseInfoState.NEED_INIT_DATA, PepcConst.PhaseInfoState.INIT);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCancelGameGroupData(PepcPhaseComponentAttr attr, List<PepcTeam> teams) {
        //完成保存状态
        boolean saveTag = saveActGroupState(attr, PepcConst.ActState.GAME_CANCEL);
        if (!saveTag) {
            log.warn("already saveGroupData");
            return;
        }

        List<Long> id = teams.stream().map(PepcTeam::getId).toList();
        if (CollectionUtils.isNotEmpty(id)) {
            pepcTeamMapper.updateTeamState(attr.getActId(), id, PepcConst.PhaseTeamState.FAIL);
        }
    }

    private boolean saveActGroupState(PepcPhaseComponentAttr attr, int state) {
        return commonDataDao
                .hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), PepcConst.ActInfoField.ACT_INFO_KEY, PepcConst.ActInfoField.ACT_STATE, Convert.toString(state));

    }

    public boolean isGroupComplete(PepcPhaseComponentAttr attr) {
        return getActState(attr) == PepcConst.ActState.GROUP_COMPLETED;
    }

    public int getActState(PepcPhaseComponentAttr attr) {
        String state = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), PepcConst.ActInfoField.ACT_INFO_KEY, PepcConst.ActInfoField.ACT_STATE);
        return Convert.toInt(state, 0);
    }


    private MakeTeamGroupResult makeTeamGroup(PepcPhaseComponentAttr attr, List<PepcTeam> teams) {
        List<PepcTeam> notEnoughTeam = teams.stream()
                .filter(p -> p.getMemberCnt() < attr.getTeamMinMember())
                .toList();

        List<PepcTeam> effectTeams = teams.stream()
                .filter(p -> p.getMemberCnt() >= attr.getTeamMinMember())
                //按id顺序排，如果队伍超限了，
                .sorted(Comparator.comparing(PepcTeam::getId))
                .toList();

        List<List<PepcTeam>> splitTeam = MyListUtils.splitList(effectTeams, attr.getStartGameMaxTeam());
        List<PepcTeam> joinGameTeam = splitTeam.get(0);
        List<PepcTeam> overLimitTeams = splitTeam.get(1);


        List<PepcTeamGroup> realJoinGameGroups = divideTeamsIntoGroups(attr, joinGameTeam, attr.getFirstPhaseIdExcludeSignUp(), false);

        MakeTeamGroupResult result = new MakeTeamGroupResult();
        result.setOverLimitTeams(overLimitTeams);
        result.setNotEnoughMemberTeams(notEnoughTeam);
        result.setRealJoinGameGroups(realJoinGameGroups);
        return result;
    }

    public static List<PepcTeamGroup> divideTeamsIntoGroups(PepcPhaseComponentAttr attr, List<PepcTeam> teams, int phaseId, boolean lastPhase) {
        final int groupMode = attr.getGroupMode();
        int baseGroupCount = teams.size() / groupMode;
        int addGroupCount = teams.size() % groupMode > 0 ? 1 : 0;
        int groupCount = baseGroupCount + addGroupCount;

        List<PepcTeamGroup> result = new ArrayList<>();
        String[] groupCodes = new String[26];
        for (int i = 0; i < 26; i++) {
            groupCodes[i] = String.valueOf((char) ('A' + i));
        }

        int index = 0;
        for (PepcTeam team : teams) {
            PepcTeamGroup group = new PepcTeamGroup();
            group.setActId(team.getActId());
            String groupCode = groupCodes[index % groupCount];
            group.setGroupCode(groupCode);
            group.setTeamId(team.getId());
            group.setCaptainUid(team.getCreator());
            group.setPhaseId(phaseId);
            String groupName = lastPhase ? "总决赛" : groupCode + "组赛";
            group.setGroupName(groupName);
            group.setCreateTime(new Date());
            result.add(group);
            index++;
        }
        return result;
    }

    public List<PepcGroupPhaseVo> queryGroupPhase(PepcPhaseComponentAttr attr) {
        List<PepcGroupPhaseVo> result = Lists.newArrayList();

        List<PepcTeamGroup> teamGroups = pepcTeamGroupMapper.selectDistinctGroupPhase(attr.getActId());
        teamGroups = teamGroups.stream().filter(p -> !p.getPhaseId().equals(attr.getLastPhaseId())).toList();

        for (PepcTeamGroup group : teamGroups) {
            PepcGroupPhaseVo vo = new PepcGroupPhaseVo();
            vo.setGroupCode(group.getGroupCode());
            vo.setGroupName(group.getGroupName());
            vo.setPhaseId(group.getPhaseId());

            Optional<PepcPhaseComponentAttr.PepcPhaseConfig> phaseConfig = attr.getPhaseConfig()
                    .stream()
                    .filter(p -> p.getPhaseId().equals(group.getPhaseId()))
                    .findFirst();
            phaseConfig.ifPresent(pepcPhaseConfig -> vo.setStartTime(pepcPhaseConfig.getStartTime().getTime()));

            result.add(vo);
        }

        //分组数据还没生成
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        //小组赛还没晋级的时候，决赛分组还没生成，这里手工加入下
        Optional<PepcPhaseComponentAttr.PepcPhaseConfig> lastPhase = attr.getPhaseConfig()
                .stream()
                .filter(p -> p.getPhaseId().equals(attr.getLastPhaseId()))
                .findFirst();
        PepcGroupPhaseVo lastVo = new PepcGroupPhaseVo();
        lastVo.setGroupCode("A");
        lastVo.setGroupName("总决赛");
        lastVo.setPhaseId(attr.getLastPhaseId());
        lastVo.setStartTime(lastPhase.get().getStartTime().getTime());
        result.add(lastVo);

        return result;
    }


    public PepcTeamRankVo queryGroupRank(PepcPhaseComponentAttr attr, String groupCode, Integer phaseId) {
        long actId = attr.getActId();
        List<PepcTeamRankItemVo> teamRank = Lists.newArrayList();

        List<PepcTeamSchedule> schedules = pepcDao.getPepcTeamSchedule(actId, phaseId, groupCode);
        List<PepcGameTeam> gameTeams = pepcDao.getGameTeam(actId, phaseId, groupCode);
        for (PepcTeamSchedule schedule : schedules) {
            PepcTeamRankItemVo item = new PepcTeamRankItemVo();
            item.setTeamId(schedule.getTeamId());
            item.setTotalScore(schedule.getScore() / attr.getScoreExtraDigits());

            List<PepcGameTeam> curGameTeam = gameTeams
                    .stream()
                    .filter(p -> p.getGroupCode().equals(schedule.getGroupCode())
                                 && p.getTeamId().equals(schedule.getTeamId()))
                    .toList();
            List<PepcRoundScoreVo> roundScore = Lists.newArrayList();
            for (PepcGameTeam gameTeamItem : curGameTeam) {
                item.setTeamName(gameTeamItem.getTeamName());
                item.setCaptainUid(gameTeamItem.getCaptainUid());

                PepcRoundScoreVo roundScoreItem = new PepcRoundScoreVo();
                roundScoreItem.setRound(gameTeamItem.getRound());
                roundScoreItem.setScore(gameTeamItem.getScore() / attr.getScoreExtraDigits());
                roundScore.add(roundScoreItem);
            }
            item.setRoundScore(roundScore);

            teamRank.add(item);
        }

        //队长头像
        List<Long> uids = teamRank.stream().map(PepcTeamRankItemVo::getCaptainUid).toList();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(uids, Template.unknown);
        teamRank.forEach(p -> {
            UserInfoVo captain = userInfoVoMap.get(p.getCaptainUid());
            p.setHeader(captain != null ? captain.getAvatarUrl() : "");
        });

        //按照分数倒排序
        teamRank = teamRank.stream().sorted(Comparator.comparing(PepcTeamRankItemVo::getTotalScore).reversed()).toList();

        PepcTeamRankVo vo = new PepcTeamRankVo();
        vo.setTeamRank(teamRank);
        return vo;
    }

    public PepcGroupTeamVo queryGroupTeam(Long actId, Long cmptInx, String groupCode, Integer phaseId) {
        PepcGroupTeamVo vo = new PepcGroupTeamVo();
        List<PepcGroupTeamItemVo> teamVos = Lists.newArrayList();

        List<PepcTeamGroup> teamGroups = pepcDao.getPepcTeamGroup(actId, phaseId, groupCode);
        if (CollectionUtils.isEmpty(teamGroups)) {
            return vo;
        }

        List<Long> teamIds = teamGroups.stream().map(PepcTeamGroup::getTeamId).toList();

        List<PepcTeam> pepcTeams = pepcTeamMapper.selectByIds(teamIds);

        List<PepcTeamMember> teamMembers = pepcTeamMemberMapper.listTeamMembers(teamIds);
        Map<Long, List<PepcTeamMember>> teamMemberMap = teamMembers.stream().collect(Collectors.groupingBy(PepcTeamMember::getTeamId));

        List<Long> memberUids = teamMembers.stream().map(PepcTeamMember::getMemberUid).toList();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(memberUids, Template.unknown);

        for (PepcTeam team : pepcTeams) {
            PepcGroupTeamItemVo itemVo = new PepcGroupTeamItemVo();
            itemVo.setTeamId(team.getId());
            itemVo.setTeamName(team.getTeamName());
            List<PepcTeamMember> members = teamMemberMap.get(team.getId());

            List<PepcTeamMemberVo> memberVos = Lists.newArrayList();
            for (PepcTeamMember member : members) {
                PepcTeamMemberVo memberVo = new PepcTeamMemberVo();
                memberVo.setUid(member.getMemberUid());
                UserInfoVo userInfoVo = userInfoVoMap.get(member.getMemberUid());
                if (userInfoVo != null) {
                    memberVo.setHeader(userInfoVo.getAvatarUrl());
                }
                memberVos.add(memberVo);
            }
            itemVo.setMembers(memberVos);

            teamVos.add(itemVo);
        }


        vo.setTeams(teamVos);
        return vo;
    }


    @Data
    private static class MakeTeamGroupResult {
        List<PepcTeamGroup> realJoinGameGroups;
        List<PepcTeam> notEnoughMemberTeams;
        List<PepcTeam> overLimitTeams;
    }
}
