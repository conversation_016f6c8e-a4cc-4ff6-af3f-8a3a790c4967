/**
 * TaskProgressChanged.java / 2020年7月29日 下午3:24:56
 * <p>
 * Copyright (c) 2020, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.activity.bean.hdzt;

import java.util.List;
import java.util.Map;

/**
 * 重复顶级任务进度变化事件
 *
 * <AUTHOR>
 * @date 2022年05月20日 上午12:09:35
 */
public class RepeatTopTaskChanged extends BaseEvent {

    public static final long URI = BaseEvent.REPEAT_TOP_TASK_CHANGED;

    /**
     * 业务id
     **/
    private long busiId;
    /**
     * 阶段标识
     **/
    private long phaseId;
    /**
     * 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分
     **/
    private long timeKey;
    /**
     * 本次触发的 闯关轮数
     **/
    private long roundComplete;
    /**
     * 开始站，从0开始，0表示还没有过站
     **/
    private long startTaskIndex;
    /**
     * 当前完成站，从0开始，0表示还没有过站
     **/
    private long currTaskIndex;
    /**
     * 整个活动范围内，当前在经历的轮数 currRound 从1开始（注：减1就是实际已循环的轮数）
     **/
    private long currRound;
    /**
     * 项目任务级别所需数量，List序就是任务等级序，从低到高
     **/
    private Map<String, List<Long>> itemLevelPassMap;
    /**
     * 项目当前数量
     **/
    private Map<String, Long> itemCurrNumMap;

    /**
     * key 是参与者角色， val 是具体的参与者标识值
     **/
    private Map<Long, String> actors;
    /**
     * 计分成员
     **/
    private String member;

    /**
     * 事件源出现时间（yyyy-MM-dd HH:mm:ss)（同榜单更新上报的时间，timestamp是消息实例发送的时间）
     **/
    private String occurTime;

    public RepeatTopTaskChanged() {
        super(URI);
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getTimeKey() {
        return timeKey;
    }

    public void setTimeKey(long timeKey) {
        this.timeKey = timeKey;
    }

    public long getRoundComplete() {
        return roundComplete;
    }

    public void setRoundComplete(long roundComplete) {
        this.roundComplete = roundComplete;
    }

    public long getStartTaskIndex() {
        return startTaskIndex;
    }

    public void setStartTaskIndex(long startTaskIndex) {
        this.startTaskIndex = startTaskIndex;
    }

    public long getCurrTaskIndex() {
        return currTaskIndex;
    }

    public void setCurrTaskIndex(long currTaskIndex) {
        this.currTaskIndex = currTaskIndex;
    }

    public long getCurrRound() {
        return currRound;
    }

    public void setCurrRound(long currRound) {
        this.currRound = currRound;
    }

    public Map<String, List<Long>> getItemLevelPassMap() {
        return itemLevelPassMap;
    }

    public void setItemLevelPassMap(Map<String, List<Long>> itemLevelPassMap) {
        this.itemLevelPassMap = itemLevelPassMap;
    }

    public Map<String, Long> getItemCurrNumMap() {
        return itemCurrNumMap;
    }

    public void setItemCurrNumMap(Map<String, Long> itemCurrNumMap) {
        this.itemCurrNumMap = itemCurrNumMap;
    }

    public Map<Long, String> getActors() {
        return actors;
    }

    public void setActors(Map<Long, String> actors) {
        this.actors = actors;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }

    public String getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(String occurTime) {
        this.occurTime = occurTime;
    }
}
