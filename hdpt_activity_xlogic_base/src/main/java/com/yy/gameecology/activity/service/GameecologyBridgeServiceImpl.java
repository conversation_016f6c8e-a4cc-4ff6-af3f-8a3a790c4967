/**
 * GameEcologyBridgeServiceImpl.java / 2019年8月26日 下午9:06:00
 * <p>
 * Copyright (c) 2019, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */
package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.client.thrift.BridgeGamebabyActivityThriftClient;
import com.yy.gameecology.activity.client.thrift.JiaoyouProxyThriftClient;
import com.yy.gameecology.activity.commons.enums.Business;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.GeBridgeHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.MDCUtils;
import com.yy.gameecology.handler.annotation.GeHandler;
import com.yy.gameecology.handler.config.GeHandlerMapping;
import com.yy.gameecology.handler.exception.GeHandlerException;
import com.yy.gameecology.handler.model.GeHandlerInvoker;
import com.yy.thrift.gameecology_bridge.*;
import com.yy.thrift.gameecology_bridge.*;
import com.yy.thrift.gameecology_bridge.GameecologyBridgeService.Iface;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019年8月26日 下午9:06:00
 */
public class GameecologyBridgeServiceImpl implements Iface {

    private static final Logger log = LoggerFactory.getLogger(GameecologyBridgeServiceImpl.class);

    public static final String VERSION = "1.0.0";

    @Autowired
    private GeHandlerMapping geHandlerMapping;
    @Autowired
    private BridgeGamebabyActivityThriftClient bridgeGamebabyActivityThriftClient;
    @Autowired
    private JiaoyouProxyThriftClient jiaoyouProxyThriftClient;

    private final Tracer TRACER = GlobalOpenTelemetry.getTracer(GameecologyBridgeServiceImpl.class.getName());

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void ping() throws TException {
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public String version() throws TException {
        RpcContext context = RpcContext.getContext();
        String l = context.getLocalAddressString();
        String r = context.getRemoteAddressString();
        return VERSION + "#" + System.currentTimeMillis() + ", C=" + r + ", S=" + l;
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public StringResponse read(StringRequest request) throws TException {
        Clock clock = new Clock();
        String seq = request.getSeq();
        int rid = request.getRid();
        int fid = request.getFid();
        StringResponse resp = null;
        boolean readCheckSign = Const.GEPM.getParamValueToBoolean("gameecology_bridge_read_check_sign",true);
        if (readCheckSign && !GeBridgeHelper.verify(request)) {
            resp = GeBridgeHelper.makeResponse(seq, 9999, "签名错误！", rid, fid);
        } else {
            Span span = TRACER.spanBuilder(String.format("rid=%d fid=%d", rid, fid)).startSpan();
            try (Scope ignored = span.makeCurrent()) {
                GeHandlerInvoker handler = geHandlerMapping.findHandler(GeHandler.Method.read, rid, fid);
                MDCUtils.putContext(String.format("rid=%d fid=%d", rid, fid));
                resp = (StringResponse) handler.invoke(request);
            } catch (GeHandlerException g) {
                resp = GeBridgeHelper.makeResponse(seq, g.getCode(), g.getMessage(), rid, fid);
            } catch (Throwable e) {
                log.error("[read] err:{} request:{} {}", e.getMessage(), request, clock.tag(), e);
                resp = GeBridgeHelper.makeResponse(seq, 500, "服务错误！", rid, fid);
            } finally {
                MDCUtils.clearContext();
                span.end();
            }
        }
        log.info("read done@req:{}, resp:{} {}", GeBridgeHelper.toString(request), GeBridgeHelper.toString(resp), clock.tag());
        return resp;
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public StringResponse write(StringRequest request) throws TException {
        log.info("write request:{}", request);
        Clock clock = new Clock();
        String seq = request.getSeq();
        int rid = request.getRid();
        int fid = request.getFid();
        StringResponse resp = null;
        boolean wirteCheckSign = Const.GEPM.getParamValueToBoolean("gameecology_bridge_write_check_sign",true);
        if (wirteCheckSign && !GeBridgeHelper.verify(request)) {
            resp = GeBridgeHelper.makeResponse(seq, 9999, "签名错误！", rid, fid);
        } else {
            Span span = TRACER.spanBuilder(String.format("rid=%d fid=%d", rid, fid)).startSpan();
            try (Scope ignored = span.makeCurrent()) {
                GeHandlerInvoker handler = geHandlerMapping.findHandler(GeHandler.Method.write, rid, fid);
                MDCUtils.putContext(String.format("rid=%d fid=%d", rid, fid));
                resp = (StringResponse) handler.invoke(request);
            } catch (GeHandlerException g) {
                resp = GeBridgeHelper.makeResponse(seq, g.getCode(), g.getMessage(), rid, fid);
            } catch (Throwable e) {
                log.error("[write] err:{} request:{} {}", e.getMessage(), request, clock.tag(), e);
                resp = GeBridgeHelper.makeResponse(seq, 500, "服务错误！", rid, fid);
            } finally {
                MDCUtils.clearContext();
                span.end();
            }
        }
        log.info("write done@req:{}, resp:{} {}", GeBridgeHelper.toString(request), GeBridgeHelper.toString(resp), clock.tag());
        return resp;
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public BinaryResponse readBinary(BinaryRequest request) throws TException {
        Clock clock = new Clock();
        String seq = request.getSeq();
        int rid = request.getRid();
        int fid = -1;
        BinaryResponse resp = null;
        if (!GeBridgeHelper.verify(request)) {
            resp = GeBridgeHelper.makeBinaryResponse(seq, 9999, "签名错误！", rid, fid);
        } else {
            resp = GeBridgeHelper.makeBinaryResponse(seq, 0, "gameecology activity readBinary test ok", rid, fid);
        }
        //log.info("readBinary done@req:{}, resp:{} {}", GeBridgeHelper.toString(request), GeBridgeHelper.toString(resp), clock.tag());
        return resp;
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public BinaryResponse writeBinary(BinaryRequest request) throws TException {
        Clock clock = new Clock();
        String seq = request.getSeq();
        int rid = request.getRid();
        int fid = -1;
        BinaryResponse resp = null;
        if (!GeBridgeHelper.verify(request)) {
            resp = GeBridgeHelper.makeBinaryResponse(seq, 9999, "签名错误！", rid, fid);
        } else {
            resp = GeBridgeHelper.makeBinaryResponse(seq, 0, "gameecology activity writeBinary test ok", rid, fid);
        }
        log.info("writeBinary done@req:{}, resp:{} {}", GeBridgeHelper.toString(request), GeBridgeHelper.toString(resp), clock.tag());
        return resp;
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void forward(long suid, long uid, long sid, long ssid, String userip, Map<String, String> extdat,
                        StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void unicastByUid(long uid, long sid, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void unicastBySuid(long suid, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void unicastByUids(List<Long> receiveUids, long sid, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void unicastBySuids(List<Long> receiveSuids, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2SubChannel(long sid, long ssid, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2TopChannel(long sid, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2SubChannels(List<TopSubChannel> ssids, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2TopChannels(List<Long> sids, StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2Template(StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
    }

    /*
     * <AUTHOR>
     * @date 2019年8月26日 下午9:06:20
     */
    @Override
    public void broadcast2AllChannels(StringRequest request) throws TException {
        if (!GeBridgeHelper.verify(request)) {
            return;
        }
        JSONObject extjson = Optional.ofNullable(JSON.parseObject(request.extjson)).orElse(new JSONObject());
        extjson.put("pass", true);
        request.setExtjson(extjson.toJSONString());
        log.info("broadcast2AllChannels request:{}", request);
        int source = request.getSource();
        if (Business.jiaoyou.code == source) {
            jiaoyouProxyThriftClient.broadcast2AllChannels(request);
        }
    }

}
