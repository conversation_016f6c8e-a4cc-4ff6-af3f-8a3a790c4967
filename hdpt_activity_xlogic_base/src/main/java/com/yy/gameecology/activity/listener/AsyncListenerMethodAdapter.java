package com.yy.gameecology.activity.listener;


import com.yy.aomi.sdk.core.Trace;
import com.yy.aomi.sdk.core.TracerHolder;
import com.yy.gameecology.activity.Aomi;
import com.yy.gameecology.common.consts.Const;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.ApplicationListenerMethodAdapter;

import java.lang.reflect.Method;

/**
 * @Author: CXZ
 * @Desciption: 异步事件监听器
 * @Date: 2021/6/1 12:51
 * @Modified:
 */
public class AsyncListenerMethodAdapter extends ApplicationListenerMethodAdapter {
    protected Logger log = LoggerFactory.getLogger(this.getClass());


    public AsyncListenerMethodAdapter(String beanName, Class<?> targetClass, Method method) {
        super(beanName, targetClass, method);
    }

    /**
     * 比默认注解小，使得异步监听器比同步监听器先执行
     * @return
     */
    @Override
    public int getOrder() {
        if (super.getOrder() == 0) {
            return -1;
        }
        return super.getOrder();
    }

    /**
     * 异步执行监听器方法
     * @param event
     */
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        final Trace asyncTrace = TracerHolder.getTracer().start("AsyncListener", false, Aomi.HANDLE_EXECUTOR_ASYNC_LISTENER_TRACE_CONTEXT);
        Const.EXECUTOR_ASYNC_LISTENER.execute(() -> {
            try {
                super.onApplicationEvent(event);
                //event 不能tojson,因为有的不可序列化会报错
                log.info("Async onApplicationEvent done@method:{},event:{}", getMethodInfo(), event);
            } catch (Throwable e) {
                log.error("Async onApplicationEvent exception@method:{},event:{}, msg:{} {}", getMethodInfo(),  event, e.getMessage(), e);
            } finally {
                TracerHolder.getTracer().end(asyncTrace);
            }
        });
    }

    /**
     * 获取执行的方法签名
     * @return
     */
    protected String getMethodInfo() {
        String errorMsg = getDetailedErrorMessage(getTargetBean(), "");
        String methodInfo = errorMsg.split("\n")[3];
        methodInfo = methodInfo.replace("Method [", "").replace("]", "").trim();
        return methodInfo;
    }


}
