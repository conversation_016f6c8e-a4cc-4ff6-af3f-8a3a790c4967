package com.yy.gameecology.activity.service.layer.itembuilder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.processor.ranking.BaseRankingProcessor;
import com.yy.gameecology.activity.service.ActRoleRankMapService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.ActRoleRankMapService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupportService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.ActorInfoStatus;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.consts.RankMapType;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.db.model.gameecology.ActRoleRankMap;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.attr.ActLayerConfigComponentAttr;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:主播&top1神豪，组合在cp榜查询的分数
 *
 * @createBy 曾文帜
 * @create 2021-07-14 11:02
 **/
@Component
public class CpTop1Builder extends ActLayerInfoService implements LayerItemBuilder {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActRoleRankMapService actRoleRankMapService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private LayerSupportService layerSupportService;

    @Autowired
    private EnrollmentNewService enrollmentService;

    @Override
    public Set<String> getItemKeys() {
        return Set.of(LayerItemTypeKey.CP_TOP_1, LayerItemTypeKey.CP_TOP_1_2);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        if (CollectionUtils.isEmpty(onlineChannel.getEffectAnchorId())) {
            return null;
        }
        return onlineChannel.getEffectAnchorId().stream().map(x -> x + "").collect(Collectors.toList());
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        //主播+用户列表
        List<LayerMemberItem> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(memberIds)) {
            return result;
        }

        long actId = actInfo.getActId();

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        //获取主播头像昵称基本信息
        List<Long> anchorUids = memberIds.stream().map(x -> Convert.toLong(x, 0)).collect(Collectors.toList());
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(anchorUids.size() * 2);
        boolean useNickExt = nickExt(viewDefine);
        Map<Long, UserInfoVo> anchorUserInfo = null;
        if (useNickExt) {
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(anchorUids);
            if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
                log.error("trying to get anchor info with nick ext fail");
            } else {
                anchorUserInfo = UserInfoService.getUserInfo(batched.getUserInfoMap());
                if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    if (MapUtils.isNotEmpty(nickExt.getUsers())) {
                        multiNickUsers.putAll(nickExt.getUsers());
                    }
                }
            }
        }

        if (anchorUserInfo == null) {
            anchorUserInfo = userInfoService.getUserInfo(anchorUids, getTemplate(actId, viewDefine.getRoleType(), actInfo.getBusiId()));
        }

        for (int i = 0; i < memberIds.size(); i++) {
            String memberId = memberIds.get(i);
            LayerMemberItem item = new LayerMemberItem();
            item.setMemberId(memberId);
            item.setItemType(viewDefine.getItemTypeKey());
            item.setState(ActorInfoStatus.NOT_IN);
            item.setViewStatus(LayerViewStatus.NOT_IN_104);
            item.setSort(i);
            UserInfoVo userInfoVo = anchorUserInfo.get(Convert.toLong(memberId, 0));
            if (userInfoVo != null) {
                String base64Nick = Base64Utils.encodeToString(Convert.toString(userInfoVo.getNick()).getBytes());
                item.setNickName(base64Nick);
                item.setLogo(userInfoVo.getAvatarUrl());
            }
            result.add(item);
        }


        //---主播贡献榜top1榜单数据
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        Map<String, EnrollmentInfo> memberMap = Maps.newHashMap();
        memberIds.forEach(x -> {
            EnrollmentInfo enrollmentInfo = enrollmentService.getFirstEnrolMember(actId, 0L, viewDefine.getRoleType(), x);
            if (enrollmentInfo == null) {
                return;
            }
            memberMap.put(x, enrollmentInfo);
            ActRoleRankMap contribution = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey(), x, enrollmentInfo.getDestRoleId(), RankMapType.CONTRIBUTION, now);
            if (contribution == null) {
                return;
            }
            QueryRankingRequest req = new QueryRankingRequest();
            req.setActId(actId);
            req.setRankingId(contribution.getRankId());
            req.setPhaseId(contribution.getPhaseId());
            req.setFindSrcMember(x);
            req.setRankingCount(1);
            //增加小时榜日榜支持
            String dateKey = "";
            if (!StringUtil.isEmpty(contribution.getDateKey())) {
                dateKey = DateUtil.format(now, contribution.getDateKey());
                req.setDateStr(dateKey);
            }
            reqMap.put(x, req);
        });

        Map<String, BatchRankingItem> conTop1 = hdztRankingThriftClient.queryBatchRanking(reqMap, null);

        //---组装查询cp榜成员分数参数
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        //key 用户uid
        List<Long> userInfoIds = Lists.newArrayList();
        for (String memberId : memberIds) {
            EnrollmentInfo enrollmentInfo = memberMap.get(memberId);
            if (enrollmentInfo == null) {
                continue;
            }

            BatchRankingItem batchRankingItem = conTop1.get(memberId);
            if (batchRankingItem != null && !CollectionUtils.isEmpty(batchRankingItem.getData())) {
                Rank userRank = batchRankingItem.getData().getFirst();

                ActorQueryItem queryItem = new ActorQueryItem();
                //cp榜member组成 用户|主播
                queryItem.setActorId(userRank.getMember() + "|" + memberId);

                ActRoleRankMap cpRankMap = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey(), memberId, enrollmentInfo.getDestRoleId(), RankMapType.CP, now);
                if (cpRankMap == null) {
                    continue;
                }
                queryItem.setRankingId(cpRankMap.getRankId());
                queryItem.setPhaseId(cpRankMap.getPhaseId());
                //增加小时榜日榜支持
                String dateKey = "";
                if (!StringUtil.isEmpty(cpRankMap.getDateKey())) {
                    dateKey = DateUtil.format(now, cpRankMap.getDateKey());
                    queryItem.setDateStr(dateKey);
                }
                queryItem.setWithStatus(false);
                queryCpScorePara.add(queryItem);

                userInfoIds.add(Convert.toLong(userRank.getMember(), 0));
            }
        }

        //如果这个是空的,尝试添加总榜的
        if (CollectionUtils.isEmpty(queryCpScorePara)) {
            queryCpScorePara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberIds, RankMapType.TOTAL, now);
        }

        //---cp成员分数
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        if (CollectionUtils.isEmpty(actorInfoItems)) {
            for (LayerMemberItem item : result) {
                //扩展信息
                Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), item);
                if (MapUtils.isNotEmpty(itemExtInfo)) {
                    item.getExt().putAll(itemExtInfo);
                }
            }
            return result;
        }

        Map<Long, UserInfoVo> userInfoVoMap = null;
        if (useNickExt) {
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(userInfoIds);
            if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
                log.error("trying to get user info with nick ext fail");
            } else {
                userInfoVoMap = UserInfoService.getUserInfo(batched.getUserInfoMap());
                if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    if (MapUtils.isNotEmpty(nickExt.getUsers())) {
                        multiNickUsers.putAll(nickExt.getUsers());
                    }
                }
            }
        }

        if (userInfoVoMap == null) {
            userInfoVoMap = userInfoService.getUserInfo(userInfoIds, getTemplate(actId, 100, actInfo.getBusiId()));
        }

        //--填充昵称、分数、排名等信息
        for (int i = 0; i < result.size(); i++) {
            LayerMemberItem item = result.get(i);
            if (i == 0 && !multiNickUsers.isEmpty()) {
                Map<String, Object> itemExt = item.getExt();
                if (itemExt == null) {
                    itemExt = new HashMap<>(5);
                    item.setExt(itemExt);
                }

                itemExt.put(NICK_EXT_KEY, multiNickUsers);
            }
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                final String memberId = item.getMemberId();
                if (actorInfoItem.getActorId().contains("|" + memberId)) {
                    item.setRank(Convert.toInt(actorInfoItem.getRank()));
                    item.setScore(actorInfoItem.getScore());
                    item.setState(ActorInfoStatus.NORMAL);
                    item.setCurRankId(actorInfoItem.getRankingId());
                    item.setCurPhaseId(actorInfoItem.getPhaseId());
                    item.setOffsetScore(actorInfoItem.getRank() != 1 ? actorInfoItem.getPreScore() - actorInfoItem.getScore() : 0);
                    EnrollmentInfo enrollmentInfo = memberMap.get(item.getMemberId());
                    if (enrollmentInfo != null) {
                        item.setRoleId(enrollmentInfo.getDestRoleId());
                    }

                    RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, actorInfoItem.getRankingId());

                    PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, actorInfoItem.getPhaseId());
                    item.setCurPhaseInfo(curPhaseInfo);
                    long leftSeconds = getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
                    item.setLeftSeconds(leftSeconds);

                    String userUid = actorInfoItem.getActorId().split("\\|")[0];
                    UserInfoVo userInfoVo = userInfoVoMap.get(Convert.toLong(userUid, 0));
                    if (userInfoVo != null) {
                        Map<String, Object> itemExt = item.getExt();
                        if (itemExt == null) {
                            itemExt = Maps.newHashMap();
                        }
                        String nick = Convert.toString(userInfoVo.getNick());
                        itemExt.put("userNick", Base64Utils.encodeToString(nick.getBytes()));
                        itemExt.put("userLogo", userInfoVo.getAvatarUrl());
                        item.setExt(itemExt);
                    }

                    //最后一个阶段晋级结算展示top n (和最后一阶段n进x的n是同一个标识)
                    int lastPhaseTopN = curPhaseInfo != null ? getLastPhaseTopN(rankingInfo, curPhaseInfo.getPhaseId()) : 0;
                    item.setLastPhaseTopN(lastPhaseTopN);

                    String topTitle = getLastPhaseTopTitle(rankingInfo, actorInfoItem.getPhaseId(), actorInfoItem.getRank());
                    item.setLastPhaseTopTitle(topTitle);

                    //界面展示状态
                    int viewStatus = layerSupportService.getViewStatus(activityInfoVo, item);
                    item.setViewStatus(viewStatus);

                    // 日排行日荣耀值
                    ActorQueryItem queryDayActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now);
                    if (queryDayActorPara != null) {
                        ActorInfoItem dayActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryDayActorPara);
                        if (dayActorItem != null) {
                            item.setDayRank(Convert.toInt(dayActorItem.getRank(), 0));
                            item.setDayScore(dayActorItem.getScore());
                        }
                    }

                    //总榜荣耀值和排名
                    ActorQueryItem queryTotalActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.TOTAL, now);
                    if (queryTotalActorPara != null) {
                        ActorInfoItem totalActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryTotalActorPara);
                        if (totalActorItem != null) {
                            item.setTotalRank(Convert.toInt(totalActorItem.getRank(), 0));
                            item.setTotalScore(totalActorItem.getScore());
                        }
                    }
                    String passDesc = getPassDescContent(rankingInfo);
                    item.setPassDesc(passDesc);
                    //扩展信息
                    Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), item);
                    if (MapUtils.isNotEmpty(itemExtInfo)) {
                        item.getExt().putAll(itemExtInfo);
                    }
                    break;
                } else if (actorInfoItem.getActorId().equals(memberId)) {
                    //总榜
                    RankingInfo rankingInfo = null;
                    PhaseInfo curPhaseInfo = null;
                    EnrollmentInfo enrollmentInfo = memberMap.get(item.getMemberId());
                    if (enrollmentInfo != null) {
                        item.setRoleId(enrollmentInfo.getDestRoleId());
                        ActRoleRankMap cpRankMap = actRoleRankMapService.getActRoleRankMap(actId, viewDefine.getItemTypeKey(), memberId, enrollmentInfo.getDestRoleId(), RankMapType.CP, now);
                        if (cpRankMap != null) {
                            curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, cpRankMap.getPhaseId());
                            rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, cpRankMap.getRankId());
                            item.setState(ActorInfoStatus.NORMAL);
                            item.setCurRankId(cpRankMap.getRankId());
                            item.setCurPhaseId(cpRankMap.getPhaseId());

                            Map<String, Object> itemExt = item.getExt();
                            if (itemExt == null) {
                                itemExt = Maps.newHashMap();
                            }

                            itemExt.put("userNick", Base64Utils.encodeToString("None".getBytes(StandardCharsets.UTF_8)));
                            itemExt.put("userLogo", "https://gamebaby.bs2.yy.com/manager/none_20230509210425.PNG");
                            item.setExt(itemExt);
                        }
                    }

                    if (curPhaseInfo == null) {
                        curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, actorInfoItem.getPhaseId());
                    }

                    if (rankingInfo == null) {
                        rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, actorInfoItem.getRankingId());
                    }
                    item.setCurPhaseInfo(curPhaseInfo);
                    long leftSeconds = getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
                    item.setLeftSeconds(leftSeconds);

                    // 日排行日荣耀值
                    ActorQueryItem queryDayActorPara = genQueryActorInfoPara(actInfo, viewDefine, busiId, memberId, RankMapType.DAY, now);
                    if (queryDayActorPara != null) {
                        ActorInfoItem dayActorItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryDayActorPara);
                        if (dayActorItem != null) {
                            item.setDayRank(Convert.toInt(dayActorItem.getRank(), 0));
                            item.setHourRank(Convert.toInt(dayActorItem.getRank(), 0));
                            item.setDayScore(dayActorItem.getScore());
                        }
                    }

                    //总榜荣耀值和排名
                    item.setTotalRank(Convert.toInt(actorInfoItem.getRank(), 0));
                    item.setTotalScore(actorInfoItem.getScore());

                    //最后一个阶段晋级结算展示top n (和最后一阶段n进x的n是同一个标识)
                    int lastPhaseTopN = curPhaseInfo != null ? getLastPhaseTopN(rankingInfo, curPhaseInfo.getPhaseId()) : 0;
                    item.setLastPhaseTopN(lastPhaseTopN);

                    String topTitle = getLastPhaseTopTitle(rankingInfo, actorInfoItem.getPhaseId(), actorInfoItem.getRank());
                    item.setLastPhaseTopTitle(topTitle);

                    //界面展示状态
                    int viewStatus = layerSupportService.getViewStatus(activityInfoVo, item);
                    item.setViewStatus(viewStatus);
                    String passDesc = getPassDescContent(rankingInfo);
                    item.setPassDesc(passDesc);
                }
            }
        }

        return result;
    }

    /**
     * 决定从那个数据源获取用户信息
     **/
    private Template getTemplate(long actId, int roleType, long busiId) {
        ActLayerConfigComponentAttr attr = layerConfigService.getLayerAttrConfig(actId);
        Integer source = attr.getUserInfoSourceMap().get(roleType);
        if (source == null) {
            return Template.unknown;
        }
        return Template.getTemplate(Convert.toInt(source, Template.unknown.getCode()));
    }

    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        target.getExtMemberItem().addAll(source);
    }


}
