package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/2 10:09
 **/
@Data
public class RankMigrationComponentAttr extends ComponentAttr {
    /**
     * 榜单迁移配置
     **/
    @ComponentAttrField(labelText = "迁移配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = MigrationConfig.class)})
    private List<MigrationConfig> migrationConfigs;
    /**
     * 验证配置
     * 累榜的时候,可以同时累计到一个验证的功能榜,让后通过读取源榜数据与该榜单数据进行对比
     * 1. 存在源榜但不存在验证的功能榜中
     * 2. 源榜与功能榜的分值不同
     * 上述两种情况下，需要重新处理
     **/
    @ComponentAttrField(labelText = "验证配置", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ValidConfig.class)})
    private List<ValidConfig> validConfigs;

    /**
     * 收到事件后,延迟迁移榜单的时间
     **/
    private int migrationDelaySeconds;
    /**
     * 延迟验证的时间,确保验证在迁移动作完成之后
     **/
    private int validDelaySeconds;


    @Data
    public static class MigrationConfig {
        /**
         * 来源活动
         **/
        @ComponentAttrField(labelText = "来源活动")
        private long sourceActId;
        /**
         * 来源榜单
         **/
        @ComponentAttrField(labelText = "来源榜单")
        private int sourceRankId;
        /**
         * 来源榜单阶段
         **/
        @ComponentAttrField(labelText = "来源榜单阶段")
        private long sourcePhaseId;
        /**
         * 累榜的业务id
         **/
        @ComponentAttrField(labelText = "累榜的业务id")
        private int targetBusiId;
        /**
         * 累榜的角色id,为0时,则读取来源活动对应的角色
         **/
        @ComponentAttrField(labelText = "累榜的角色id")
        private long targetActorId;
        /**
         * 累榜的itemId
         **/
        @ComponentAttrField(labelText = "累榜的itemId")
        private String targetItemId;
        /**
         * 基准分数,会将原榜单的分值减去该基准分值,累加到新的榜单上
         * 只能配置大于等于0的数值
         * 该值存在的意义是：有些榜单的分值是以时间戳为值的,当新旧榜单都存在该member时，直接累加则该member会飙升到第一名
         **/
        @ComponentAttrField(labelText = "迁移时扣减的分数")
        private long baseScore= 1_000_000_000;
        /**
         * 收到事件后,延迟迁移榜单的时间
         **/
        @ComponentAttrField(labelText = "延迟迁移的秒数")
        private int delaySeconds;

    }

    @Data
    public static class ValidConfig {
        /**
         * 来源活动
         **/
        @ComponentAttrField(labelText = "来源活动")
        private long sourceActId;
        /**
         * 来源榜单
         **/
        @ComponentAttrField(labelText = "来源榜单")
        private int sourceRankId;
        /**
         * 来源榜单阶段
         **/
        @ComponentAttrField(labelText = "来源榜单阶段")
        private int sourcePhaseId;
        /**
         * 验证榜单id
         **/
        @ComponentAttrField(labelText = "验证榜单id")
        private int targetRankId;
        /**
         * 验证榜单阶段id
         **/
        @ComponentAttrField(labelText = "验证榜单阶段id")
        private int targetPhaseId;
        /**
         * 延迟验证的时间,确保验证在迁移动作完成之后
         **/
        @ComponentAttrField(labelText = "延迟验证的时间", remark = "确保验证在迁移动作完成之后")
        private int delaySeconds;
    }

    @Data
    public static class DelayEvent {
        private MigrationConfig migrationConfig;
        private ValidConfig validConfig;
        private Long actId;
    }
}
