package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 夏日探险玩法组件属性配置
 */
@Data
public class SummerAdventureComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", remark = "送礼业务ID")
    private int busiId;

    @ComponentAttrField(labelText = "活动礼物配置", remark = "活动礼物ID和价值配置",
            subFields = {
                    @SubField(fieldName = "giftId", type = String.class, labelText = "礼物ID"),
                    @SubField(fieldName = "value", type = Double.class, labelText = "礼物价值(元)")
            })
    private List<GiftConfig> giftConfigs = Lists.newArrayList();

    @ComponentAttrField(labelText = "骰子获取阈值", remark = "单次送礼获得骰子的金额阈值(元)", defaultValue = "131.4")
    private double diceThreshold = 131.4;

    @ComponentAttrField(labelText = "骰子上限", remark = "单个CP活动期间累计最多获得骰子数量", defaultValue = "500")
    private int maxDiceCount = 500;

    @ComponentAttrField(labelText = "单次消耗上限", remark = "每次参与探险时最多消耗骰子数量", defaultValue = "30")
    private int maxDicePerRound = 30;

    @ComponentAttrField(labelText = "地图格子奖励配置", remark = "30个格子的奖励配置",
            subFields = {
                    @SubField(fieldName = "position", type = Integer.class, labelText = "格子位置(1-30)"),
                    @SubField(fieldName = "rewardType", type = String.class, labelText = "奖励类型"),
                    @SubField(fieldName = "rewardName", type = String.class, labelText = "奖励名称"),
                    @SubField(fieldName = "rewardIcon", type = String.class, labelText = "奖励图标"),
                    @SubField(fieldName = "packageId", type = Long.class, labelText = "奖包ID"),
                    @SubField(fieldName = "stockRule", type = String.class, labelText = "库存规则"),
                    @SubField(fieldName = "stockThreshold", type = Integer.class, labelText = "库存阈值")
            })
    private List<MapReward> mapRewards = Lists.newArrayList();

    @ComponentAttrField(labelText = "发奖任务ID", remark = "发奖系统的任务ID")
    private long welfareTaskId;

    @ComponentAttrField(labelText = "抽奖任务ID", remark = "抽奖系统的任务ID")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "广播配置", remark = "全服广播相关配置",
            subFields = {
                    @SubField(fieldName = "minBroadcastValue", type = Double.class, labelText = "最小广播价值(元)"),
                    @SubField(fieldName = "broadcastTemplate", type = Integer.class, labelText = "广播模板")
            })
    private BroadcastConfig broadcastConfig = new BroadcastConfig();

    @ComponentAttrField(labelText = "黑名单检查", remark = "是否启用黑名单检查", defaultValue = "true")
    private boolean enableBlacklistCheck = true;

    @ComponentAttrField(labelText = "统计推送配置", remark = "生产群推送配置",
            subFields = {
                    @SubField(fieldName = "webhookKey", type = String.class, labelText = "机器人webhook key"),
                    @SubField(fieldName = "pushInterval", type = Integer.class, labelText = "推送间隔(分钟)")
            })
    private PushConfig pushConfig = new PushConfig();

    @Data
    public static class GiftConfig {
        @ComponentAttrField(labelText = "礼物ID")
        private String giftId;
        
        @ComponentAttrField(labelText = "礼物价值(元)")
        private double value;
    }

    @Data
    public static class MapReward {
        @ComponentAttrField(labelText = "格子位置(1-30)")
        private int position;
        
        @ComponentAttrField(labelText = "奖励类型")
        private String rewardType;
        
        @ComponentAttrField(labelText = "奖励名称")
        private String rewardName;
        
        @ComponentAttrField(labelText = "奖励图标")
        private String rewardIcon;
        
        @ComponentAttrField(labelText = "奖包ID")
        private long packageId;
        
        @ComponentAttrField(labelText = "库存规则")
        private String stockRule;
        
        @ComponentAttrField(labelText = "库存阈值")
        private int stockThreshold;
    }

    @Data
    public static class BroadcastConfig {
        @ComponentAttrField(labelText = "最小广播价值(元)", defaultValue = "52.0")
        private double minBroadcastValue = 52.0;
        
        @ComponentAttrField(labelText = "广播模板", defaultValue = "1")
        private int broadcastTemplate = 1;
    }

    @Data
    public static class PushConfig {
        @ComponentAttrField(labelText = "机器人webhook key", defaultValue = "activity.robot.webhook")
        private String webhookKey = "activity.robot.webhook";
        
        @ComponentAttrField(labelText = "推送间隔(分钟)", defaultValue = "30")
        private int pushInterval = 30;
    }
}
