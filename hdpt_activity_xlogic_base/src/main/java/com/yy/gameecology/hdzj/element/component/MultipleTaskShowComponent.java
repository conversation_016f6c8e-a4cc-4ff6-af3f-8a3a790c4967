package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.acttask.*;
import com.yy.gameecology.activity.service.HdztTaskService;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.MultipleTaskShowComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * desc:任务展示组件
 *
 * <AUTHOR>
 * @date 2023-02-22 20:37
 **/
@RequestMapping("/cmpt/multipleTaskShow")
@RestController
@Component
public class MultipleTaskShowComponent extends BaseActComponent<MultipleTaskShowComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public Long getComponentId() {
        return ComponentId.MULTIPLE_TASK_SHOW;
    }

    /**
     * 完成任务的排序加权
     */
    private final static int COMPLETE_WEIGHTING = 1000000;

    @Autowired
    private HdztTaskService hdztTaskService;

    @Autowired
    private LoginService loginService;

    @RequestMapping("/queryTask")
    public Response<List<TaskGroupShowVo>> queryTask(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {
        MultipleTaskShowComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            return Response.fail(-1, "index配置错误");
        }
        long uid = loginService.getLoginYYUid(req, resp);
        if (uid <= 0) {
            //兼容未登录的情况 弹幕游戏
            log.info("queryTask uid<=0,index:{},actId:{}",index,actId);
            uid = 0L;
        }
        if (MapUtils.isEmpty(attr.getGroupConfig()) || MapUtils.isEmpty(attr.getGroupTask())) {
            return Response.success(Lists.newArrayList());
        }

        List<TaskGroupShowVo> group = queryTaskGroup(actId, Convert.toString(uid), attr);
        return Response.success(group);

    }

    public List<TaskGroupShowVo> queryTaskGroup(long actId, String member, MultipleTaskShowComponentAttr attr) {
        List<TaskGroupShowVo> result = Lists.newArrayList();

        for (String groupCode : attr.getGroupConfig().keySet()) {
            TaskGroupShowConfig groupConfig = attr.getGroupConfig().get(groupCode);
            List<TaskShowConfig> taskConfigs = attr.getGroupTask().get(groupCode);
            if (CollectionUtils.isEmpty(taskConfigs)) {
                continue;
            }
            //任务组配置
            TaskGroupShowVo groupShowVo = buildGroupShowVo(groupCode, groupConfig);
            //任务组内任务数据
            List<TaskShowVo> tasks = queryTaskShowItem(actId, member, attr, taskConfigs);
            log.info("MultipleTaskShowComponent queryTaskShowItem actId:{},member:{},tasks:{}",actId,member, JSON.toJSON(tasks));
            //任务组是否完成任务
            long completeTaskCount = tasks.stream().filter(TaskShowVo::isComplete).count();
            boolean groupComplete = groupConfig.getMinCompleteTask() > 0 ? completeTaskCount >= groupConfig.getMinCompleteTask()
                    : completeTaskCount >= tasks.size();
            groupShowVo.setComplete(groupComplete);
            if (attr.completeLowPriority && groupComplete) {
                //已完成任务降低展示权重
                groupShowVo.setSort(groupShowVo.getSort() * COMPLETE_WEIGHTING);
            }
            groupShowVo.setTask(tasks);
            result.add(groupShowVo);
        }
        //展示排序
        result = result.stream()
                .sorted(Comparator.comparing(TaskGroupShowVo::getSort))
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 任务组配置
     */
    private TaskGroupShowVo buildGroupShowVo(String groupCode, TaskGroupShowConfig config) {
        TaskGroupShowVo group = new TaskGroupShowVo();
        group.setGroupCode(groupCode);
        group.setGroupName(config.getName());
        group.setSort(config.getSort());
        group.setShowDetails(config.isShowDetails());
        return group;
    }


    private List<TaskShowVo> queryTaskShowItem(long actId, String member, MultipleTaskShowComponentAttr attr, List<TaskShowConfig> configs) {
        List<TaskShowVo> tasks = Lists.newArrayList();
        Date now = commonService.getNow(actId);
        for (TaskShowConfig config : configs) {
            TaskShowVo taskShowVo = new TaskShowVo();
            taskShowVo.setTaskName(config.getTaskName());
            taskShowVo.setAwardUrl(config.getAwardUrl());
            taskShowVo.setRankId(config.getRankId());
            String dateCode = "";
            if (StringUtil.isNotBlank(config.getRankDateFormat())) {
                dateCode = DateUtil.format(now, config.getRankDateFormat());
            }
            List<TaskShowItemVo> itemVos = hdztTaskService.queryTaskItem(actId, config.getRankId(), config.getPhaseId(), dateCode, member);

            for (TaskShowItemVo vo : itemVos) {
                long completeCount = vo.getCompletedCount();
                long taskTotal = vo.getTaskTotalCount();
                long allTaskTotal = vo.getAllTaskTotalCount();

                //配置了精度转换
                if (StringUtil.isNotBlank(config.getScoreScale())) {
                    BigDecimal scale = new BigDecimal(config.getScoreScale());
                    if (scale.longValue() != 0) {
                        completeCount = new BigDecimal(vo.getCompletedCount()).divide(scale, Const.ZERO, RoundingMode.DOWN).longValue();
                        taskTotal = new BigDecimal(vo.getTaskTotalCount()).divide(scale, Const.ZERO, RoundingMode.DOWN).longValue();
                        allTaskTotal = new BigDecimal(vo.getAllTaskTotalCount()).divide(scale, Const.ZERO, RoundingMode.DOWN).longValue();
                        log.info("scale change,actId:{},member:{},rankId:{},scale:{},completeBefore:{},complete:{},totalBefore:{},total:{},allTotalBefore:{},allTotal:{}",
                                actId, member, config.getRankId(), scale, vo.getCompletedCount(), completeCount, vo.getTaskTotalCount(), taskTotal, vo.getAllTaskTotalCount(), allTaskTotal);

                    }
                }
                //配置了分子不能大于分母
                if (attr.isCompleteFixScore() && completeCount > allTaskTotal) {
                    completeCount = allTaskTotal;
                }
                vo.setTaskTotalCount(taskTotal);
                vo.setCompletedCount(completeCount);
                vo.setAllTaskTotalCount(allTaskTotal);

            }
            //数据
            taskShowVo.setTaskItem(itemVos);
            tasks.add(taskShowVo);
        }

        return tasks;
    }


}
