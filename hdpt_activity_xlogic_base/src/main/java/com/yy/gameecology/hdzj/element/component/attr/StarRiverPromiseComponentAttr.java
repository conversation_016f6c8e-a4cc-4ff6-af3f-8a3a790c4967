package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 星河之约组件属性配置
 * <AUTHOR> Assistant
 * @date 2025-07-03
 */
@Data
public class StarRiverPromiseComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId = 200;

    @ComponentAttrField(labelText = "厅角色ID", remark = "用于获取CP所在房间")
    private String tingRoleId;

    @ComponentAttrField(labelText = "CP榜单ID", remark = "用于获取CP关系的榜单")
    private long cpRankId;

    @ComponentAttrField(labelText = "活动礼物配置", remark = "礼物ID到心动值的映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "礼物ID"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "心动值")
            })
    private Map<String, Integer> giftValueMap = Maps.newHashMap();

    @ComponentAttrField(labelText = "拼图获取阈值", remark = "每日CP值达到这些阈值时获得拼图碎片", 
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
            })
    private List<Long> puzzleThresholds;

    @ComponentAttrField(labelText = "阶段配置", remark = "三个阶段的配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "阶段编号(1-3)"),
                    @SubField(fieldName = Constant.VALUE, type = StageConfig.class, labelText = "阶段配置")
            })
    private Map<Integer, StageConfig> stageConfigs = Maps.newHashMap();

    @ComponentAttrField(labelText = "奖池配置")
    private GiftPoolConfig giftPoolConfig;

    @ComponentAttrField(labelText = "口令抽奖配置")
    private LotteryConfig lotteryConfig;

    @ComponentAttrField(labelText = "特效配置", remark = "浪漫约会特效配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "阶段编号"),
                    @SubField(fieldName = Constant.VALUE, type = EffectConfig.class, labelText = "特效配置")
            })
    private Map<Integer, EffectConfig> effectConfigs = Maps.newHashMap();

    @ComponentAttrField(labelText = "广播配置")
    private BroadcastConfig broadcastConfig;

    /**
     * 阶段配置
     */
    @Data
    public static class StageConfig {
        @ComponentAttrField(labelText = "阶段名称")
        private String stageName;

        @ComponentAttrField(labelText = "拼图总数", remark = "默认9个")
        private int puzzleCount = 9;

        @ComponentAttrField(labelText = "横向奖励配置", remark = "3行奖励配置",
                subFields = {
                        @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "行号(1-3)"),
                        @SubField(fieldName = Constant.VALUE, type = LineReward.class, labelText = "奖励配置")
                })
        private Map<Integer, LineReward> rowRewards = Maps.newHashMap();

        @ComponentAttrField(labelText = "竖向奖励配置", remark = "3列奖励配置",
                subFields = {
                        @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "列号(1-3)"),
                        @SubField(fieldName = Constant.VALUE, type = LineReward.class, labelText = "奖励配置")
                })
        private Map<Integer, LineReward> colRewards = Maps.newHashMap();

        @ComponentAttrField(labelText = "全碎片奖励配置")
        private AllPuzzleReward allPuzzleReward;
    }

    /**
     * 连线奖励配置
     */
    @Data
    public static class LineReward {
        @ComponentAttrField(labelText = "奖励名称")
        private String rewardName;

        @ComponentAttrField(labelText = "奖励图片")
        private String rewardPic;

        @ComponentAttrField(labelText = "奖励天数")
        private int rewardDays;

        @ComponentAttrField(labelText = "任务ID")
        private long taskId;

        @ComponentAttrField(labelText = "礼包ID")
        private long packageId;
    }

    /**
     * 全碎片奖励配置
     */
    @Data
    public static class AllPuzzleReward {
        @ComponentAttrField(labelText = "主持奖励礼物名称")
        private String anchorGiftName;

        @ComponentAttrField(labelText = "主持奖励任务ID")
        private long anchorTaskId;

        @ComponentAttrField(labelText = "主持奖励礼包ID")
        private long anchorPackageId;

        @ComponentAttrField(labelText = "神豪奖励礼物名称")
        private String userGiftName;

        @ComponentAttrField(labelText = "神豪奖励任务ID")
        private long userTaskId;

        @ComponentAttrField(labelText = "神豪奖励礼包ID")
        private long userPackageId;
    }

    /**
     * 奖池配置
     */
    @Data
    public static class GiftPoolConfig {
        @ComponentAttrField(labelText = "奖池总金额", remark = "单位：元")
        private long totalAmount = 50000;

        @ComponentAttrField(labelText = "兜底奖励名称")
        private String backupRewardName = "夏日飞骏入场秀3天";

        @ComponentAttrField(labelText = "兜底奖励任务ID")
        private long backupTaskId;

        @ComponentAttrField(labelText = "兜底奖励礼包ID")
        private long backupPackageId;
    }

    /**
     * 口令抽奖配置
     */
    @Data
    public static class LotteryConfig {
        @ComponentAttrField(labelText = "口令内容")
        private String password = "佳偶天成，爱满人间";

        @ComponentAttrField(labelText = "抽奖奖励配置", remark = "奖励配置列表",
                subFields = {
                        @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = LotteryReward.class)
                })
        private List<LotteryReward> rewards;
    }

    /**
     * 抽奖奖励配置
     */
    @Data
    public static class LotteryReward {
        @ComponentAttrField(labelText = "奖励名称")
        private String rewardName;

        @ComponentAttrField(labelText = "每日上限", remark = "0表示无限制")
        private int dailyLimit;

        @ComponentAttrField(labelText = "概率", remark = "百分比，如5表示5%")
        private int probability;

        @ComponentAttrField(labelText = "任务ID")
        private long taskId;

        @ComponentAttrField(labelText = "礼包ID")
        private long packageId;

        @ComponentAttrField(labelText = "数量")
        private int amount = 1;
    }

    /**
     * 特效配置
     */
    @Data
    public static class EffectConfig {
        @ComponentAttrField(labelText = "特效名称")
        private String effectName;

        @ComponentAttrField(labelText = "特效持续时间", remark = "秒")
        private int duration = 8;

        @ComponentAttrField(labelText = "特效资源URL")
        private String effectUrl;
    }

    /**
     * 广播配置
     */
    @Data
    public static class BroadcastConfig {
        @ComponentAttrField(labelText = "广播模板")
        private String broadcastTemplate = "恭喜{nick1}和{nick2}点亮拼图碎片，获得{reward}！";

        @ComponentAttrField(labelText = "广播业务ID")
        private int broBusiId = 200;

        @ComponentAttrField(labelText = "横幅ID")
        private long bannerId;
    }
}
