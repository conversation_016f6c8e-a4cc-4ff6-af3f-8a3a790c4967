package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardLimitConfig;
import lombok.Data;

import java.util.Map;

/**
 * desc:发奖限额控制
 *
 * <AUTHOR>
 * @date 2025-03-06 11:05
 **/
@Data
public class LimitControlComponentAttr extends ComponentAttr {
   @ComponentAttrField(labelText = "日任务总奖池限额", remark = "",
           subFields = {
                   @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "限额id,如：奖包id"),
                   @SubField(fieldName = Constant.VALUE, type = AwardLimitConfig.class, labelText = "限额配置")
           })
   private Map<Long, AwardLimitConfig> awardTotalLimit = Maps.newLinkedHashMap();
}
