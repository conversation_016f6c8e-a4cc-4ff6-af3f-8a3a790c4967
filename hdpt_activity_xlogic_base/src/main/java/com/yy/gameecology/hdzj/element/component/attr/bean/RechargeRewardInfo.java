package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * <AUTHOR> 2023/8/25
 */
@Data
public class RechargeRewardInfo {

    @ComponentAttrField(labelText = "礼包id")
    private Integer rewardId;

    @ComponentAttrField(labelText = "礼包名")
    private String rewardName;

    @ComponentAttrField(labelText = "礼包图标")
    private String rewardIcon;

    @ComponentAttrField(labelText = "消耗积分")
    private long costValue;



    @ComponentAttrField(labelText = "返利金额")
    private Integer rebateValue;

    @ComponentAttrField(labelText = "返利货币")
    private String rebateName;

    @ComponentAttrField(labelText = "返利图标")
    private String rebateIcon;

    @ComponentAttrField(labelText = "返利比例")
    private String rebateRate;

    @ComponentAttrField(labelText = "返利日份数")
    private Integer rebateDayLimit;

    @ComponentAttrField(labelText = "返利发奖id")
    private Long rebatePackageId;



    @ComponentAttrField(labelText = "入场秀名")
    private String showName;

    @ComponentAttrField(labelText = "入场秀图标")
    private String showIcon;

    @ComponentAttrField(labelText = "入场秀天数")
    private Integer showDays;

    @ComponentAttrField(labelText = "入场秀发奖id")
    private Long showPackageId;



    @ComponentAttrField(labelText = "豪标名")
    private String signName;

    @ComponentAttrField(labelText = "豪标图标")
    private String signIcon;

    @ComponentAttrField(labelText = "豪标天数")
    private Integer signDays;

    @ComponentAttrField(labelText = "豪标发奖id")
    private Long signPackageId;

}
