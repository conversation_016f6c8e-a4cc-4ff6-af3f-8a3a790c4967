package com.yy.gameecology.hdzj.bean.pepc;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-08 17:52
 **/
@Data
public class PepcTeamRankItemVo {


    /**
     * 队长uid
     */
    private Long captainUid;

    /**
     * 队长头像
     */
    private String header;
    private long teamId;
    private String teamName;
    private long totalScore;
    private List<PepcRoundScoreVo> roundScore;
}
