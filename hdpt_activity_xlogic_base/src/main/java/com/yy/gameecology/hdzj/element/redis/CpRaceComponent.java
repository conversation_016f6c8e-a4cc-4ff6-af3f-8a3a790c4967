package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.event.SkillCardSeatChgKafkaEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.race.*;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpRaceComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-07-11 11:55
 **/
@UseRedisStore
@RequestMapping("/cpRace")
@RestController
@Component
public class CpRaceComponent extends BaseActComponent<CpRaceComponentAttr> implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    protected WebdbThriftClient webdbThriftClient;

    @Autowired
    private SignedService signedService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private RetryTool retryTool;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    private static final int TOP_APP_BANNER_CONTENT_TYPE = 6;

    private static final long TOPONE_BRO_BANNER_ID = 5096001L;

    private static final long LATEST_GIFT_CP_BRO_BANNER_ID = 5096002L;

    private static final String TOPONE_NOTICE_TYPE = Convert.toString(ComponentId.CP_RACE);

    /**
     * 获奖弹窗提醒（不在频道时的缓存）
     */
    private static final String TOP_ONE_ENTER_CHANNEL_NOTICE = "top_one_enter_channel_notice";

    /**
     * 主播最后收礼频道
     * <p>
     * TODO 监听榜单变化事件，记录每个主播最近一次收礼频道
     */
    private static final String LAST_RECEIVE_GIFT_CHANNEL = "last_receive_gift_channel:%s";

    /**
     * 送祝福、抽大奖
     */
    private final String LATEST_PASSWORD_LOTTERY ="latest_password_lottery";

    /**
     * 我的中奖记录
     */
    private final String MY_AWARD_LIST = "my_award_list:%s";

    /**
     * 全局中奖记录
     */
    private final String WHOLE_AWARD_LIST = "whole_award_list";

    /**
     * 鹊桥成就，大于100万
     */
    private final String HISTORY_TERMINUS_TOP_ONE_LIST = "history_terminus_top_one_list";


    /**
     * 鹊桥竞速第一名
     */
    private static final String CP_RACE_TOPONE_STATISTIC = "cp_race_topone_statistics";

    /**
     * 发放礼物总金额
     */
    private static final String CP_RACE_TOTAL_AWARD_STATISTIC = "cp_race_total_award_statistics";

    /**
     * 每个主持最新cp
     */
    private static final String CP_RACE_LATEST_CP = "cp_race_latest_cp:%s";


    /**
     * 是否参加过抽大奖
     */
    public static final String VISIT_BEST_CP = "visit_best_cp:%s";

    /**
     * 用户进房只发一次口令抽奖
     */
    public static final String INSERT_ROOM_SEND_TOAST = "insert_room_send_toast:%s";



    public static final String FIRST_ONSEAT_HAS_NOTICE = "first_onseat_has_notice";


    /**
     * 挂件上最新送礼toast
     */
    public static final String LATEST_GIFT_TOAST_FREQUENCY = "latest_gift_toast_frequency:%s:%d:%d";
    public static final String TARGET_STR = "情定此生世世相随";


    @Override
    public Long getComponentId() {
        return ComponentId.CP_RACE;
    }


    //记录当时段cp 最后一次送礼房间
    @HdzjEventHandler(value = RankingScoreChanged.class,canRetry = false)
    public void onRankScoreChange(RankingScoreChanged event, CpRaceComponentAttr attr) {
        log.info("onRankingScoreChanged actId:{}, event:{}", attr.getActId(), JSONUtils.toJsonString(event));
        if(event.getRankId() != attr.getRankId()) {
            return;
        }

        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getOccurTime()));
        String[] members = event.getMember().split("\\|");
        String userUid = members[0];
        String babyUid = members[1];

        //更新当前主持最新CP，以事件到来的顺序判断
        String redisGroup = getRedisGroupCode(attr.getActId());
        String latestKey = getLatestCPKey(attr, timeCode);
        actRedisDao.hset(redisGroup,latestKey,babyUid,userUid);

        //记录当前主持送礼时最新room，兼容没有传厅的情况
        String key = makeKey(attr, String.format(LAST_RECEIVE_GIFT_CHANNEL, timeCode));
        String value = event.getActors().get(attr.getTingActor());
        if(StringUtils.isNotEmpty(value)) {
            actRedisDao.hset(redisGroup, key, babyUid, value);

            //发送最新送礼toast
            broLatestGiftCp(attr,timeCode,Long.parseLong(userUid),Long.parseLong(babyUid),value);
        }



    }

    private void broLatestGiftCp(CpRaceComponentAttr attr,String timeCode,long userUid,long babyUid,String roomId) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(LATEST_GIFT_TOAST_FREQUENCY,timeCode,userUid,babyUid));
        if (actRedisDao.setNX(groupCode, key, StringUtil.ONE,5)) {
            String[] array = roomId.split("_");
            long sid = Convert.toLong(array[0]);
            long ssid = Convert.toLong(array[1]);

            List<Long> uids = Lists.newArrayList(userUid,babyUid);
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);

            Map<String, Object> ext = Maps.newHashMap();
            UserInfoVo userInfoVo = userInfos.get(userUid);
            UserInfoVo babyInfoVo = userInfos.get(babyUid);
            ext.put("userUid",userUid);
            ext.put("babyUid",babyUid);
            if(userInfoVo!=null ){
                ext.put("userNick",userInfoVo.getNick());
            }

            if(babyInfoVo!=null) {
                ext.put("babyNick",babyInfoVo.getNick());
            }
            ext.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));

            commonBroadCastService.commonBannerBroadcast(sid, ssid, 0,  com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), 2
                    , attr.getActId(), 0L, 0L, LATEST_GIFT_CP_BRO_BANNER_ID, 0L, ext);


        }else{
            log.info("broLatestGiftCp toast ferquecy ignore ,userUid:{},babyUid:{}",userUid,babyUid);
        }

    }

    private String getLatestCPKey(CpRaceComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(CP_RACE_LATEST_CP, timeCode));
    }


    public boolean textMatch(String chatText) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return containsTargetSequence(cleanedInput, TARGET_STR);
    }

    public static boolean containsTargetSequence(String input, String target) {
        int targetIndex = 0;
        for (int i = 0; i < input.length(); i++) {
            if (input.charAt(i) == target.charAt(targetIndex)) {
                targetIndex++;
                if (targetIndex == target.length()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 结算发奖、弹窗、超1万广播特效、写口令抽奖标记,发奖记录
     * 注意：这里活动结束后的时间临界问题，结束后还会发奖
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, CpRaceComponentAttr attr) {
        log.info("handlePhaseTimeEnd event:{},attr:{}", JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode, Const.ONE, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("handlePhaseTimeEnd empty rank,actId:{},rankId:{},phaseId:{},timeCode:{}", event.getActId(), event.getRankId(), event.getPhaseId(), timeCode);
            return;
        }
        String[] members = ranks.get(0).getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long anchorUid = Long.parseLong(members[1]);
        long score = ranks.get(0).getScore();

        //发奖
        AwardAttrConfig awardAttrConfig = handlePhaseTimeEndAward(event, attr, userUid, anchorUid, score,timeCode);

        //保存发奖记录数据，口令抽奖标记
        handlePhaseTimeEndSaveData(event, attr, awardAttrConfig, userUid, anchorUid,timeCode,score);

        //TOP 1 特效广播
        handlePhaseTimeBanner(event, attr, userUid, anchorUid, score);

        //横幅广播
        broTopOneCpBanner(userUid,anchorUid,attr,timeCode,awardAttrConfig);

        //用户弹窗
        String noticeSeq = makeKey(attr, "seq:toponenotice:" + seq);
        retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            try {
                Thread.sleep(7000);
                handlePhaseTimePopup(event, attr, awardAttrConfig, userUid, anchorUid, score);
            } catch (InterruptedException e) {
                log.error("delay send unicast interrupted:", e);
            }

        },Const.GENERAL_POOL);


    }


    private void broTopOneCpBanner(long userUid ,long babyUid, CpRaceComponentAttr attr,String timeCode,AwardAttrConfig awardAttrConfig) {
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        ext.put("giftLogo",awardAttrConfig.getAwardIcon());
        ext.put("giftAmount",awardAttrConfig.getAwardAmount());
        ext.put("giftCount",awardAttrConfig.getNum());
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick",userInfoVo.getNick());
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",babyInfoVo.getNick());
        }
        ext.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));

        String cpLastRoom = makeKey(attr, String.format(LAST_RECEIVE_GIFT_CHANNEL, timeCode));
        String sidSsidStr = actRedisDao.hget(getRedisGroupCode(attr.getActId()), cpLastRoom,String.valueOf(babyUid));
        long sid;
        long ssid;
        if(sidSsidStr != null) {
            String[] sidSsid = sidSsidStr.split("_");
            sid = Convert.toLong(sidSsid[0]);
            ssid = Convert.toLong(sidSsid[1]);
        } else {
            ssid = 0L;
            sid = 0L;
        }

        ext.put("sid",sid);
        ext.put("ssid",ssid);

        log.info("CpRaceComponent broLatestCpBanner commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), 4
                , attr.getActId(), 0L,0L, TOPONE_BRO_BANNER_ID, 0L, ext);


    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpRaceComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{}", uid, attr.getActId(), extJson, sid);
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }

        //TOP1 获奖弹窗
        String topKey = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(redisGroup, topKey, Convert.toString((uid)));
        if (StringUtils.isNotEmpty(value)) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, value, StringUtils.EMPTY, uid);
            actRedisDao.hdel(redisGroup, topKey, Convert.toString((uid)));
            log.info("onUserEnterTemplate popup done,noteType:{},uid:{},data:{}", TOPONE_NOTICE_TYPE, uid, value);
        }

        //发放进房口令，一个用户只发送一次
        String currentTing = sid + "_" + ssid;
        String lastBestCpTing = getLastBestCpTing(attr);
        if(lastBestCpTing != null && lastBestCpTing.contains(currentTing)) {
            String groupCode = getRedisGroupCode(attr.getActId());
            Date now = commonService.getNow(attr.getActId());
            String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30)), DateUtil.PATTERN_TYPE9);
            String key = makeKey(attr, String.format(INSERT_ROOM_SEND_TOAST, timeCode));
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if(first) {
                JSONObject chatJson = new JSONObject();
                chatJson.put("text", "公屏发送“情定此生，世世相随”可参与口令抽奖！");
                chatJson.put("sid", sid);
                chatJson.put("ssid", ssid);
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5096_cp_room_toast", JsonUtil.toJson(chatJson) ,StringUtils.EMPTY
                        , event.getUid());
            }

        }

    }


    /**
     * 口令抽奖
     */
    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = false)
    public void onChannelTextChatInnerEvent(ChannelChatTextInnerEvent event, CpRaceComponentAttr attr) {
        long sid = event.getTopsid();
        long ssid = event.getSubsid();
        long uid = event.getUid();
        String currentTing = sid + "_" + ssid;
        String lastBestCpTing = getLastBestCpTing(attr);
        String seq = event.getSeq();
        if(textMatch(event.getChat()) && StringUtils.isNotEmpty(lastBestCpTing) && lastBestCpTing.contains(currentTing)) {
            Date now = commonService.getNow(attr.getActId());
            String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30)), DateUtil.PATTERN_TYPE9);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String key = makeKey(attr, String.format(VISIT_BEST_CP, timeCode));
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if(first){
                String time =DateUtil.format(now);
                String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_chat_"+ timeCode + "_" + uid));
                BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                        uid, attr.getLotteryTaskId(), 1, 0, lotterySeq);
                log.info("onChannelTextChatInnerEvent uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
                Response<List<CpRaceComponentAttr.Award>> response = lotteryAward(result, attr);
                if(response.success()) {
                    JSONObject extJson = new JSONObject();
                    extJson.put("hit", false);
                    if(!response.getData().isEmpty()) {
                        CpRaceComponentAttr.Award award = response.getData().get(0);
                        extJson.put("name", award.getName());
                        extJson.put("icon", award.getImg());
                        extJson.put("awardCount", 1);
                        extJson.put("hit", true);
                    }
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5096_chatAward", extJson.toJSONString()
                            ,StringUtils.EMPTY , uid);
                }
            }else{
                log.warn("onChannelTextChatInnerEvent not first uid:{},sid:{},ssid:{}",uid,sid,ssid);
            }
        }else{
            log.info("onChannelTextChatInnerEvent not match uid:{},sid:{},ssid:{},chat:{}",uid,sid,ssid,event.getChat());
        }




    }

    public Response<List<CpRaceComponentAttr.Award>> lotteryAward(BatchLotteryResult batchLotteryResult, CpRaceComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<CpRaceComponentAttr.Award> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }

        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                CpRaceComponentAttr.Award award = new CpRaceComponentAttr.Award();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }


    public Map<Long, AwardModelInfo> packageInfoMap(CpRaceComponentAttr attr) {
        try {

            Map<Long, AwardModelInfo> visit = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            return  visit == null ? Collections.emptyMap() : visit;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    public String getLastBestCpTing(CpRaceComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30)), DateUtil.PATTERN_TYPE9);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
        String value = actRedisDao.hget(redisGroup, lotteryKey,timeCode);

        if(StringUtils.isNotEmpty(value)) {
            LotteryInfoVo bestCpInfo = JSON.parseObject(value, LotteryInfoVo.class);
            return bestCpInfo.getSid() + "_" + bestCpInfo.getSsid();

        }

        return "";
    }


    private AwardAttrConfig handlePhaseTimeEndAward(PhaseTimeEnd event, CpRaceComponentAttr attr, long userUid, long anchorUid, long score,String timeCode) {
        AwardAttrConfig award = attr.findAward(score);
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String welfareTime = DateUtil.getNowYyyyMMddHHmmss();
        String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr, seq + "_award_user_" + timeCode+ "_" + userUid));
        hdztAwardServiceClient.doWelfareV2(welfareTime, attr.getBusiId(), userUid, award.getTAwardTskId(), award.getNum(), award.getTAwardPkgId(), userHashSeq, Maps.newHashMap());

        String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr, seq + "_award_anchor_" +timeCode+ "_" + anchorUid));
        //异步发奖
        hdztAwardServiceClient.doWelfareV2(welfareTime, attr.getBusiId(), anchorUid, award.getTAwardTskId(), award.getNum(), award.getTAwardPkgId(), babyHashSeq, Maps.newHashMap());

        return award;
    }

    /**
     * 阶段结算-写口令抽奖标记、发奖记录
     */
    private void handlePhaseTimeEndSaveData(PhaseTimeEnd event, CpRaceComponentAttr attr, AwardAttrConfig award, long userUid, long anchorUid,String timeCode,long score) {
        log.info("handlePhaseTimeEndSaveData userUid:{},anchorUid:{},timeCode:{},score:{}",userUid,anchorUid,timeCode,score);
        String redisGroup = getRedisGroupCode(attr.getActId());
        if(score>=attr.getBannerMinScore()){
            //写频道口令抽奖标记
            UserCurrentChannel channel = getAnchorAwardChannel(attr, anchorUid,timeCode);
            if (channel != null) {
                String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
                LotteryInfoVo value = new LotteryInfoVo();
                value.setUserUid(userUid);
                value.setBabyUid(anchorUid);
                value.setSid(channel.getTopsid());
                value.setSsid(channel.getSubsid());
                value.setSettleTime(System.currentTimeMillis());
                actRedisDao.hset(redisGroup, lotteryKey,timeCode,JsonUtil.toJson(value),attr.getLotterySxpireSecond());
            }else{
                log.warn("handlePhaseTimeEndSaveData password lottery channel is null,userUid:{},anchorUid:{},timeCode:{},score:{}",userUid,anchorUid,timeCode,score);
            }
        }

        RaceAwardInfoVo awardInfo = new RaceAwardInfoVo();
        awardInfo.setUserUid(userUid);
        awardInfo.setBabyUid(anchorUid);
        awardInfo.setAwardTime(timeCode);
        awardInfo.setAwardName(award.getNum()+"个"+award.getAwardName()+"（价值"+award.getAwardAmount()+"元)");
        awardInfo.setHasBroadcast(score>=attr.getBannerMinScore()?true : false);
        String content = JSON.toJSONString(awardInfo);
        //写我的中奖记录
        String userRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, userUid));
        String anchorRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, anchorUid));
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String userSeq = makeKey(attr,"seq:userawardrecord:"+ seq + "_" + userUid);
        actRedisDao.lPushWithSeq(redisGroup, userSeq, userRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        String anchorSeq = makeKey(attr,"seq:anchorawardrecord:"+ seq + anchorUid );
        actRedisDao.lPushWithSeq(redisGroup, anchorSeq, anchorRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        //写全服中奖记录
        String wholeKey = makeKey(attr, WHOLE_AWARD_LIST);
        String wholdSeq = makeKey(attr,"seq:wholdawardrecord:"+seq + "_" + userUid+"_" + anchorUid);
        actRedisDao.lPushWithSeq(redisGroup, wholdSeq, wholeKey, content, DateUtil.ONE_DAY_SECONDS);

        //鹊桥成就
        if(score>=attr.getBannerMinScore()) {
            String historyKey = makeKey(attr, HISTORY_TERMINUS_TOP_ONE_LIST);
            String historySeq = makeKey(attr,"seq:historyTopRecord:"+seq + "_" + userUid+"_" + anchorUid);
            actRedisDao.lPushWithSeq(redisGroup, historySeq, historyKey, content, DateUtil.ONE_DAY_SECONDS);
        }

        //数据统计
        saveToponeStatistics(attr,userUid,anchorUid,timeCode,score,seq,award);
    }

    private void saveToponeStatistics(CpRaceComponentAttr attr, long userUid, long babyUid, String timeCode, long score, String seq, AwardAttrConfig award) {
        UserCurrentChannel channel = getAnchorAwardChannel(attr, babyUid,timeCode);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String toponeKey = makeKey(attr,CP_RACE_TOPONE_STATISTIC);
        RaceRoadMapCpVo toponeInfo = new RaceRoadMapCpVo();
        toponeInfo.setUserUid(userUid);
        toponeInfo.setBabyUid(babyUid);
        toponeInfo.setScore(score);
        if(channel!=null){
            toponeInfo.setSid(channel.getTopsid());
            toponeInfo.setSsid(channel.getSubsid());
        }
        actRedisDao.hset(redisGroup,toponeKey,timeCode, JsonUtil.toJson(toponeInfo));


        //累计发放金额
        String totalAwardKey = makeKey(attr,CP_RACE_TOTAL_AWARD_STATISTIC);
        String totalAwardSeq = makeKey(attr,"seq:totalAwardStatis"+seq+":"+timeCode);
        actRedisDao.incrValueWithSeq(redisGroup,totalAwardSeq,totalAwardKey,2*award.getAwardAmount(),24 * 60 * 60);

    }
    /**
     * 获取口令抽奖的频道
     * 优先用主播当时在线频道，如果主播不在线，则用最近一次收礼频道
     */
    private UserCurrentChannel getAnchorAwardChannel(CpRaceComponentAttr attr, long anchorUid,String timeCode) {
        UserCurrentChannel channel = commonService.getUserCurrentChannel(anchorUid);
        if (channel != null) {
            return channel;
        }

        //最近一次收礼的频道
        String key = makeKey(attr, String.format(LAST_RECEIVE_GIFT_CHANNEL, timeCode));
        String lastReceive = actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, Convert.toString(anchorUid));
        if (StringUtils.isNotEmpty(lastReceive)) {
            channel = new UserCurrentChannel();
            String[] array = lastReceive.split("_");
            channel.setTopsid(Convert.toLong(array[0]));
            channel.setSubsid(Convert.toLong(array[1]));
            return channel;
        }

        log.error("getAnchorAwardChannel null,anchorUid:{}", anchorUid);
        return null;
    }


    /**
     * 阶段结算-婚礼特效
     */
    private void handlePhaseTimeBanner(PhaseTimeEnd event, CpRaceComponentAttr attr, long userUid, long anchorUid, long score) {
        if (score < attr.getBannerMinScore()) {
            return;
        }
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(anchorUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers,810);

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo anchorInfo = userInfos.get(anchorUid);
        ext.put("userUid", userUid);
        ext.put("anchorUid", anchorUid);
        ext.put("svgaUrl", attr.getTop1Svga());
        if (userInfoVo != null) {
            ext.put("userLogo", userInfoVo.getAvatarUrl());
            ext.put("userNick", Base64Utils.encodeToString(Convert.toString(userInfoVo.getNick()).getBytes()));
        }

        if (anchorInfo != null) {
            ext.put("anchorLogo", anchorInfo.getAvatarUrl());
            ext.put("anchorNick", Base64Utils.encodeToString(Convert.toString(anchorInfo.getNick()).getBytes()));
        }

        log.info("handlePhaseTimeBanner commonBannerBroadcast ext:{}", JSON.toJSONString(ext));
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), BroadcastType.ALL_TEMPLATE
                , attr.getActId(), 0L, 0L, ComponentId.CP_RACE, 0L, ext);


        //app cp特效
        broTopOneCpApp(event, attr, userInfos, userUid, anchorUid);

    }

    private void broTopOneCpApp(PhaseTimeEnd event, CpRaceComponentAttr attr, Map<Long, UserInfoVo> userInfoVoMap, long userUid, long anchorUid) {

        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(8);
        svgaConfig.setSvgaURL(attr.getTop1Svga());

        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, userInfoVoMap, userUid, anchorUid);
        svgaConfig.setContentLayers(broContentLayers);

        //头像
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr, userInfoVoMap, userUid, anchorUid));


        final int bcType = FstAppBroadcastType.ALL_TEMPLATE;
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        seq = seq + "_TOP1_BANNER";
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, 0, 0, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(TOP_APP_BANNER_CONTENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUidList(Lists.newArrayList(userUid, anchorUid));
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("broTopOneCpApp app done seq:{}, userUid:{} ,anchor:{},event:{}", seq, userUid, anchorUid, JSON.toJSONString(appBannerEvent));
    }

    private List<Map<String, String>> buildSvgaImageConfig(CpRaceComponentAttr attr, Map<Long, UserInfoVo> userInfoVoMap, long userUid, long babyUid) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<Integer, String> imageMap = attr.getSvgaImgLayers();
        UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfoVo = userInfoVoMap.get(babyUid);
        for (Integer userType : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String imageKey = imageMap.get(userType);
            String image;
            if (userType == 1) {
                image = babyInfoVo != null ? babyInfoVo.getAvatarUrl() : StringUtil.EMPTY;
            } else {
                image = userInfoVo != null ? userInfoVo.getAvatarUrl() : StringUtil.EMPTY;
            }
            broImgLayer.put(imageKey, image);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;


    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpRaceComponentAttr attr, Map<Long, UserInfoVo> userInfoVoMap, long userUid, long anchorUid) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();

        Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
        String svagText = attr.getSvgaText();
        //替换uid
        svagText = svagText.replace("{uidNick}","{"+userUid+":n}");
        svagText = svagText.replace("{anchorNick}", "{"+anchorUid+":n}");
        AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
        appBannerSvgaText.setText(svagText);

        if (StringUtil.isNotBlank(attr.getSvgaFontSize())) {
            appBannerSvgaText.setFontSize(JSON.parseObject(attr.getSvgaFontSize(), Map.class));
        }
        broSvgaTextLayer.put(attr.getSvgaTextKey(), appBannerSvgaText);

        if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
            broContentLayers.add(broSvgaTextLayer);
        }

        return broContentLayers;

    }

    private void handlePhaseTimePopup(PhaseTimeEnd event, CpRaceComponentAttr attr, AwardAttrConfig award, long userUid, long anchorUid, long score) {
        Map<String, Object> extInfo = Maps.newHashMap();
        extInfo.put("awardIcon", award.getAwardIcon());
        extInfo.put("awardTime", event.getEndTime());
        extInfo.put("awardName", award.getAwardName());
        extInfo.put("awardCount", award.getNum());
        extInfo.put("awardAmount", award.getAwardAmount());

        String key = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(anchorUid);
        if (userChannel != null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(extInfo), StringUtils.EMPTY, userUid);
        } else {
            log.warn("handlePhaseTimePopup userUid not in channel ,actId:{},userUid:{},time:{}", attr.getActId(), userUid, event.getEndTime());
            actRedisDao.hset(getRedisGroupCode(attr.getActId()), key, String.valueOf(userUid), JsonUtil.toJson(extInfo));
        }

        if (babyChannel != null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(extInfo), StringUtils.EMPTY, anchorUid);
        } else {
            log.warn("handlePhaseTimePopup userUid not in channel ,actId:{},anchorUid:{},time:{}", attr.getActId(), userUid, event.getEndTime());
            actRedisDao.hset(getRedisGroupCode(attr.getActId()), key, String.valueOf(anchorUid), JsonUtil.toJson(extInfo));
        }
    }


    /**
     * 查询竞速地图格子
     */
    @RequestMapping("/getRoadMapCpList")
    public Response<RaceRoadMap> getRoadMapCpList(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx,String dateStr) {


        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        RaceRoadMap rsp = new RaceRoadMap();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), dateStr,attr.getRoadMapTopN(), null);
        if(CollectionUtils.isEmpty(ranks)){
            rsp.setGiftLogo(attr.getDefaultAwardIcon());
            rsp.setGiftAmount(0L);
            rsp.setGiftCount(0);
            rsp.setBroBanner(false);
            return Response.success(rsp);
        }

        long top1Score = ranks.get(0).getScore();
        AwardAttrConfig award = attr.findAward(top1Score);
        rsp.setGiftLogo(award.getAwardIcon());
        rsp.setGiftAmount(award.getAwardAmount());
        rsp.setGiftCount(award.getNum());
        if(top1Score>=attr.getBannerMinScore()){
            rsp.setBroBanner(true);
            rsp.setTop1Svga(attr.getTop1Svga());
        }else{
            rsp.setBroBanner(false);
        }

        ranks = ranks.stream().filter(v->v.getScore()>=attr.getScoreExchangeStep()).collect(Collectors.toList());
        List<RaceRoadMapCpVo> list = Lists.newArrayListWithCapacity(ranks.size());
        Set<Long> uids = new HashSet<>();
        Set<Long> babyUids = new HashSet<>();
        for(Rank rank : ranks) {
            RaceRoadMapCpVo item = new RaceRoadMapCpVo();
            String[] members = rank.getMember().split("\\|");
            long userUid = Long.parseLong(members[0]);
            long babyUid = Long.parseLong(members[1]);
            item.setBabyUid(babyUid);
            item.setUserUid(userUid);
            item.setRank(rank.getRank());
            item.setScore(rank.getScore());
            uids.add(userUid);
            uids.add(babyUid);
            babyUids.add(babyUid);
            list.add(item);
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
        list.stream().forEach(v->{
            //获取正在开播频道
            ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(v.getBabyUid());
            if (onMicChannel != null) {
                v.setSid(onMicChannel.getSid());
                v.setSsid(onMicChannel.getSsid());
            }
            UserInfoVo userInfoVo = userInfos.get(v.getUserUid());
            UserInfoVo babyInfoVo = userInfos.get(v.getBabyUid());
            if(userInfoVo!=null){
                v.setUserNick(userInfoVo.getNick());
                v.setUserLogo(userInfoVo.getAvatarUrl());
            }
            if(babyInfoVo!=null) {
                v.setBabyNick(babyInfoVo.getNick());
                v.setBabyLogo(babyInfoVo.getAvatarUrl());
            }
        });

        rsp.setList(list);
        rsp.setNickExtUsers(multiNickUsers);



        return  Response.success(rsp);
    }


    /**
     * 获取CP列表
     */
    @RequestMapping("/getCpList")
    public Response<RaceRoadMap> getCpList(HttpServletRequest request, HttpServletResponse response,
                                           Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx, String dateStr) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登录");
        }
        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }

        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        Map<String, QueryRankingRequest> antiReqMap = Maps.newHashMap();
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        QueryRankingRequest contributeReq = new QueryRankingRequest();
        contributeReq.setActId(actId);
        contributeReq.setRankingId(attr.getCpContributeRankId());
        contributeReq.setPhaseId(attr.getPhaseId());
        contributeReq.setFindSrcMember(Convert.toString(uid));
        contributeReq.setRankingCount(attr.getRankLimit());
        contributeReq.setDateStr(dateStr);
        reqMap.put(Convert.toString(uid), contributeReq);
        Set<Long> uids = Sets.newHashSetWithExpectedSize(100);
        uids.add(uid);
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
                long toUid = Convert.toLong(rank.getMember());
                String memberId = toUid + "|" + uid;
                ActorQueryItem queryItem = new ActorQueryItem();
                queryItem.setActorId(memberId);
                queryItem.setRankingId(attr.getRankId());
                queryItem.setPhaseId(attr.getPhaseId());
                queryItem.setDateStr(dateStr);
                queryItem.setWithStatus(false);
                queryCpScorePara.add(queryItem);
            }
        }

        QueryRankingRequest antiContributeReq = new QueryRankingRequest();
        antiContributeReq.setActId(actId);
        antiContributeReq.setRankingId(attr.getCpAntiContributeRankId());
        antiContributeReq.setPhaseId(attr.getPhaseId());
        antiContributeReq.setFindSrcMember(Convert.toString(uid));
        antiContributeReq.setRankingCount(attr.getRankLimit());
        antiContributeReq.setDateStr(dateStr);
        antiReqMap.put(Convert.toString(uid), antiContributeReq);
        Map<String, BatchRankingItem> conAntiTop = hdztRankingThriftClient.queryBatchRanking(antiReqMap, null);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
                long toUid = Convert.toLong(rank.getMember());
                String memberId = uid + "|" + toUid;
                ActorQueryItem queryItem = new ActorQueryItem();
                queryItem.setActorId(memberId);
                queryItem.setRankingId(attr.getRankId());
                queryItem.setPhaseId(attr.getPhaseId());
                queryItem.setDateStr(dateStr);
                queryItem.setWithStatus(false);
                queryCpScorePara.add(queryItem);
            }
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
        List<RaceRoadMapCpVo> list = null;
        if(CollectionUtils.isNotEmpty(queryCpScorePara)){
            list = Lists.newArrayListWithCapacity(queryCpScorePara.size());
            List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                String[] members = actorInfoItem.getActorId().split("\\|");
                RaceRoadMapCpVo item = new RaceRoadMapCpVo();
                item.setUserUid(Convert.toLong(members[0], 0));
                item.setBabyUid(Convert.toLong(members[1], 0));
                item.setScore(actorInfoItem.getScore());
                item.setRank((int)actorInfoItem.getRank());
                UserInfoVo userInfoVo = userInfos.get(Convert.toLong(members[0], 0));
                UserInfoVo babyInfoVo =  userInfos.get(Convert.toLong(members[1], 0));
                if (userInfoVo != null) {
                    item.setUserNick(userInfoVo.getNick());
                    item.setUserLogo(userInfoVo.getAvatarUrl());
                }
                if(babyInfoVo!=null) {
                    item.setBabyNick(babyInfoVo.getNick());
                    item.setBabyLogo(babyInfoVo.getAvatarUrl());
                }
                //获取正在开播频道
                ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(Convert.toLong(members[1], 0));
                if (onMicChannel != null) {
                    item.setSid(onMicChannel.getSid());
                    item.setSsid(onMicChannel.getSsid());
                }


                list.add(item);
            }
            list.sort(Comparator.comparing(RaceRoadMapCpVo::getScore).reversed());
        }

        RaceRoadMap rsp = new RaceRoadMap();
        rsp.setList(list);
        rsp.setNickExtUsers(multiNickUsers);
        rsp.setUid(uid);
        rsp.setAvatar(userInfos.get(uid).getAvatarUrl());
        rsp.setSign(signedService.getSignedSidByBusiId(uid, attr.getBusiId()) > 0);

        return Response.success(rsp);
    }

    /**
     * 我的获奖记录
     */
    @RequestMapping("/queryMyAwardRecord")
    public Response<RaceAwardRsp> queryMyAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                            Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {


        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登录");
        }
        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String userRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, uid));
        RaceAwardRsp rsp = getAwardListByKey(actId,userRecordKey);

        return Response.success(rsp);
    }

    /**
     * 全服获奖记录
     */
    @RequestMapping("/queryAllAwardRecord")
    public Response<RaceAwardRsp> queryAllAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                      Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {

        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String wholeKey = makeKey(attr, WHOLE_AWARD_LIST);
        RaceAwardRsp rsp = getAwardListByKey(actId,wholeKey);

        return Response.success(rsp);

    }

    /**
     * 鹊桥成就
     */
    @RequestMapping("/historyCpTopOneList")
    public Response<RaceAwardRsp> historyCpTopOneList(HttpServletRequest request, HttpServletResponse response,
                                                      Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {

        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String redisGroup = getRedisGroupCode(actId);
        String historyKey = makeKey(attr, HISTORY_TERMINUS_TOP_ONE_LIST);

        RaceAwardRsp rsp = new RaceAwardRsp();
        List<String> awardRecord = actRedisDao.lrange(redisGroup, historyKey, 0,99);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<RaceAwardInfoVo> list = Lists.newArrayListWithCapacity(awardRecord.size());
            Set<Long> uids = Sets.newHashSetWithExpectedSize(100);
            for (String item : awardRecord) {
                RaceAwardInfoVo record = JSON.parseObject(item, RaceAwardInfoVo.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
            for(RaceAwardInfoVo item : list) {
                UserInfoVo userInfoVo = userInfos.get(item.getUserUid());
                UserInfoVo babyInfoVo =  userInfos.get(item.getBabyUid());
                if (userInfoVo != null) {
                    item.setUserNick(userInfoVo.getNick());
                    item.setUserLogo(userInfoVo.getAvatarUrl());
                }
                if(babyInfoVo!=null) {
                    item.setBabyNick(babyInfoVo.getNick());
                    item.setBabyLogo(babyInfoVo.getAvatarUrl());
                }
            }

            rsp.setList(list);
            rsp.setNickExtUsers(multiNickUsers);
        }

        return Response.success(rsp);
    }

    /**
     * 送祝福抽大奖
     */
    @RequestMapping("/getBestCp")
    public Response<LotteryCpInfo> getBestCp(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {
        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Date now = commonService.getNow(attr.getActId());
        String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30)), DateUtil.PATTERN_TYPE9);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
        String value = actRedisDao.hget(redisGroup, lotteryKey,timeCode);

        LotteryCpInfo rsp = new LotteryCpInfo();
        if(StringUtils.isNotEmpty(value)){
            LotteryInfoVo bestCpInfo = JSON.parseObject(value,LotteryInfoVo.class);

            List<Long> uids = Lists.newArrayList(bestCpInfo.getUserUid(),bestCpInfo.getBabyUid());
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
            UserInfoVo userInfoVo = userInfos.get(bestCpInfo.getUserUid());
            UserInfoVo babyInfoVo =  userInfos.get(bestCpInfo.getBabyUid());
            if (userInfoVo != null) {
                bestCpInfo.setUserNick(userInfoVo.getNick());
                bestCpInfo.setUserLogo(userInfoVo.getAvatarUrl());
            }
            if(babyInfoVo!=null) {
                bestCpInfo.setBabyNick(babyInfoVo.getNick());
                bestCpInfo.setBabyLogo(babyInfoVo.getAvatarUrl());
            }
            bestCpInfo.setStartTime(bestCpInfo.getSettleTime());
            bestCpInfo.setEndTime(bestCpInfo.getSettleTime() + attr.getLotterySxpireSecond()*1000);
            bestCpInfo.setCurrentTime(System.currentTimeMillis());

            bestCpInfo.setChatText("情定此生，世世相随");
            rsp.setCpInfo(bestCpInfo);
            rsp.setNickExtUsers(multiNickUsers);

        }

        return Response.success(rsp);

    }



    private RaceAwardRsp getAwardListByKey(long actId,String awardKey) {
        RaceAwardRsp rsp = new RaceAwardRsp();
        String redisGroup = getRedisGroupCode(actId);
        List<String> awardRecord = actRedisDao.lrange(redisGroup, awardKey, 0,499);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<RaceAwardInfoVo> list = Lists.newArrayListWithCapacity(awardRecord.size());
            Set<Long> uids = Sets.newHashSetWithExpectedSize(100);
            for (String item : awardRecord) {
                RaceAwardInfoVo record = JSON.parseObject(item, RaceAwardInfoVo.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
            for(RaceAwardInfoVo item : list) {
                UserInfoVo userInfoVo = userInfos.get(item.getUserUid());
                UserInfoVo babyInfoVo =  userInfos.get(item.getBabyUid());
                if (userInfoVo != null) {
                    item.setUserNick(userInfoVo.getNick());
                }
                if(babyInfoVo!=null) {
                    item.setBabyNick(babyInfoVo.getNick());
                }
            }

            rsp.setList(list);
            rsp.setNickExtUsers(multiNickUsers);
        }

        return rsp;
    }

    private Map<Long, UserInfoVo> getUserInfoWithNickExt(List<Long> uids,Map<String, Map<String, MultiNickItem>> multiNickUsers) {
        if(CollectionUtils.isEmpty(uids)){
            return  Maps.newHashMap();
        }

        Map<Long, UserInfoVo> userInfoVoMap = null;

        final int maxBatchSize = 499;
        List<List<Long>> batchUids = MyListUtils.subList(uids, maxBatchSize);
        for (List<Long> uidList : batchUids) {
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uidList);
            if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
                log.warn("trying to get user info with nick ext fail,uids size:{}",uids.size());
            }else{
                Map<Long, UserInfoVo> userBatchVoMap = UserInfoService.getUserInfo(batched.getUserInfoMap());
                if(MapUtils.isNotEmpty(userBatchVoMap)) {
                    if(userInfoVoMap==null){
                        userInfoVoMap = Maps.newHashMap();
                    }
                    userInfoVoMap.putAll(userBatchVoMap);
                }
                if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    if (MapUtils.isNotEmpty(nickExt.getUsers())) {
                        multiNickUsers.putAll(nickExt.getUsers());
                    }
                }
            }

        }


        if (userInfoVoMap == null) {
            userInfoVoMap = userInfoService.getUserInfo(uids, Template.getTemplate(810));
        }
        return userInfoVoMap;

    }

    @Override
    public long getActId() {
        return ComponentId.CP_RACE;
    }

    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        if (source.getActBusiId() != (long) BusiId.SKILL_CARD.getValue()) {
            return source;
        }
        List<LayerMemberItem> layerMemberItems = source.getExtMemberItem();
        if (CollectionUtils.isEmpty(layerMemberItems)) {
            return source;
        }

        long actId = source.getActId();
        CpRaceComponentAttr attr = tryGetUniqueComponentAttr(actId);
        Date now = commonService.getNow(attr.getActId());
        String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(now), DateUtil.PATTERN_TYPE9);

        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        Set<Long> userInfoIds = new HashSet<>();
        for (LayerMemberItem layerMemberItem : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(layerMemberItem.getItemType())) {
                continue;
            }
            String babyUid = layerMemberItem.getMemberId();
            userInfoIds.add(Long.parseLong(babyUid));

            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankId(), now, attr.getPhaseId());
            PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, attr.getPhaseId());
            long leftSeconds = actLayerInfoService.getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
            layerMemberItem.setCurPhaseInfo(curPhaseInfo);
            layerMemberItem.setLeftSeconds(leftSeconds);

            //获取最新的CP
            String latestUid = getLatestCpUid(attr, actId,  timeCode, Long.parseLong(babyUid));
            if (StringUtils.isEmpty(latestUid)) {
                continue;
            }

            userInfoIds.add(Long.parseLong(latestUid));
            ActorQueryItem queryItem = new ActorQueryItem();
            //cp榜member组成 用户|主播
            queryItem.setActorId(latestUid + "|" + babyUid);
            queryItem.setRankingId(attr.getRankId());
            queryItem.setPhaseId(attr.getPhaseId());
            queryItem.setDateStr(timeCode);
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }

        if (CollectionUtils.isEmpty(userInfoIds)) {
            return source;
        }

        AwardAttrConfig awardAttrConfig = getTopOneAwardConfig(attr,timeCode);
        String giftLogo = null;
        Long giftAmount = null;
        Integer giftCount = null;
        if (awardAttrConfig != null) {
            giftLogo = awardAttrConfig.getAwardIcon();
            //显示两个人的礼物金额
            giftAmount = awardAttrConfig.getAwardAmount();
            giftCount = awardAttrConfig.getNum();
        }else{
            giftLogo = attr.getDefaultAwardIcon();
            giftAmount = 0L;
            giftCount = 0;
        }
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(userInfoIds.size());
        Map<Long, UserInfoVo> userInfoVoMap = getUserInfoWithNickExt(Lists.newArrayList(userInfoIds),multiNickUsers);

        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        for (LayerMemberItem item : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(item.getItemType())) {
                continue;
            }
            final String memberId = item.getMemberId();
            Map<String, Object> itemExt = item.getExt();
            if (itemExt == null) {
                itemExt = Maps.newHashMap();
            }
            itemExt.put("giftAmount", String.valueOf(giftAmount));
            itemExt.put("giftLogo", giftLogo);
            itemExt.put("giftCount", giftCount);
            itemExt.put("babyUid", Long.parseLong(memberId));
            UserInfoVo babyInfoVo = userInfoVoMap.get(Convert.toLong(memberId, 0));
            if (babyInfoVo != null) {
                String nick = Convert.toString(babyInfoVo.getNick());
                itemExt.put("babyNick", Base64Utils.encodeToString(nick.getBytes()));
                itemExt.put("babyLogo", babyInfoVo.getAvatarUrl());
            }
            Map<String, Map<String, MultiNickItem>> nickExtUsers = new HashMap<>();
            if (multiNickUsers != null && multiNickUsers.containsKey(memberId)) {
                nickExtUsers.put(memberId, multiNickUsers.get(memberId));
            }
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                if (actorInfoItem.getActorId().contains("|" + memberId)) {
                    String userUid = actorInfoItem.getActorId().split("\\|")[0];
                    item.setRank(Convert.toInt(actorInfoItem.getRank()));
                    item.setScore(actorInfoItem.getScore());
                    item.setCurRankId(actorInfoItem.getRankingId());
                    item.setCurPhaseId(actorInfoItem.getPhaseId());

                    itemExt.put("userUid", userUid);
                    UserInfoVo userInfoVo = userInfoVoMap.get(Convert.toLong(userUid, 0));
                    if (userInfoVo != null) {
                        String nick = Convert.toString(userInfoVo.getNick());
                        itemExt.put("userNick", Base64Utils.encodeToString(nick.getBytes()));
                        itemExt.put("userLogo", userInfoVo.getAvatarUrl());

                    }

                    if (multiNickUsers != null && multiNickUsers.containsKey(String.valueOf(userUid))) {
                        nickExtUsers.put(userUid, multiNickUsers.get(userUid));
                    }
                    break;
                }
            }
            itemExt.put("nickExtUsers", nickExtUsers);
        }


        return source;


    }

    private AwardAttrConfig getTopOneAwardConfig(CpRaceComponentAttr attr,String timeCode) {
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode, Const.ONE, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("getTopOneAwardConfig empty rank,actId:{},rankId:{},phaseId:{},timeCode:{}", attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode);
            return null;
        }
        long score = ranks.get(0).getScore();
        AwardAttrConfig award = attr.findAward(score);
        return award;
    }
    private String getLatestCpUid(CpRaceComponentAttr attr,long actId,  String timeCode,long babyUid) {

        String groupCode = getRedisGroupCode(actId);
        String latestKey = getLatestCPKey(attr, timeCode);
        return actRedisDao.hget(groupCode,latestKey,String.valueOf(babyUid));
    }

    /**
     * 上下座
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = SkillCardSeatChgKafkaEvent.class, canRetry = false)
    public void onSkillCardSeatChgEvent(SkillCardSeatChgKafkaEvent event, CpRaceComponentAttr attr) {
        log.info("onSkillCardSeatChgEvent,actid:{},uid:{},type:{}", attr.getActId(), event.getUid(), event.getChangeType());
        if(SkillCardSeatChgKafkaEvent.ChangeType.ON_SEAT.equals(event.getChangeType())) {
            if (SysEvHelper.isDeploy()) {
                if (commonService.isGrey(attr.getActId()) && !hdztRankingThriftClient.checkWhiteList(attr.getActId(), RoleType.USER, String.valueOf(event.getUid()))) {
                    log.info("onSkillCardSeatChgEvent grey not in whitelist uid:{},actId:{}",event.getUid(),attr.getActId());
                    return;
                }
            }
            //主持上座发弹窗
            long family = signedService.getSignedSidByBusiId(event.getUid(), BusiId.SKILL_CARD.getValue());
            if(family>0){
                Date now = commonService.getNow(attr.getActId());
                String redisGroup = getRedisGroupCode(attr.getActId());
                String hasNoticeKey = makeKey(attr,FIRST_ONSEAT_HAS_NOTICE);
                if(actRedisDao.hsetnx(redisGroup,hasNoticeKey,String.valueOf(event.getUid()),DateUtil.format(now,DateUtil.PATTERN_TYPE1))){
                    JSONObject json = new JSONObject();
                    json.put("giftLogo", attr.getOnSeatGiftUrl());
                    json.put("weddingUrl",attr.getWeddingUrl());

                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5096_anchorFirstInsert", JsonUtil.toJson(json) ,StringUtils.EMPTY
                            , event.getUid());

                    //app弹窗
                    broAppFirstOnSeat(attr,event.getSeq(),event.getUid());
                }
            }else{
                log.info("onSkillCardSeatChgEvent family:{},uid:{}",family,event.getUid());
            }

        }
    }

    private void broAppFirstOnSeat(CpRaceComponentAttr attr,String eventSeq,long uid) {
        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(8);
        svgaConfig.setSvgaURL(attr.getAppBannerSvga());

        //头像
        svgaConfig.setImgLayers(List.of(attr.getAnchorSvgaImgLayers()));


        final int bcType = AppBannerEvent2.BC_TYPE_USER;
        String seq = "seq:firstInsert:"+ eventSeq+":"+uid;
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, 0, 0, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(3);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUidList(Lists.newArrayList(uid));
        appBannerEvent.setPushUidlist(List.of(uid));
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("broAppFirstOnSeat app done seq:{}, userUid:{} ,event:{}", seq, uid, JSON.toJSONString(appBannerEvent));
    }


    @RequestMapping("/testFirstInsertBanner")
    public Response testFirstInsertBanner(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx,long uid) {
        CpRaceComponentAttr attr = getComponentAttr(actId,cmptInx);
        broAppFirstOnSeat(attr, UUID.randomUUID().toString(),uid);

        return Response.ok();
    }


    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        Date now = commonService.getNow(actId);
        Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));
        doStaticReport(actId, now, getUniqueComponentAttr(actId),noticeDate);
        return Response.ok();
    }

    /**
     * 每半小时一次日报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "11 0/30 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpRaceComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));

            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execCpRaceStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(actId, now, attr,noticeDate);

        }
    }

    public void doStaticReport(long actId, Date now, CpRaceComponentAttr attr,Date noticeDate) {
        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{}",actId);
            return;
        }
        String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
        String noticeTime = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE12);

        StringBuilder content = new StringBuilder();
        content.append(String.format("### 场次时间：%s场\n", noticeTime));

        String groupCode = getRedisGroupCode(actId);
        String toponeKey = makeKey(attr,CP_RACE_TOPONE_STATISTIC);
        String topValue = actRedisDao.hget(groupCode,toponeKey,timeCode);
        RaceRoadMapCpVo toponeInfo = null;
        Set<Long> uids = new HashSet<>();
        if (StringUtils.isNotEmpty(topValue)) {
            toponeInfo = JSONUtils.parseObject(topValue, RaceRoadMapCpVo.class);
            uids.add(toponeInfo.getUserUid());
            uids.add(toponeInfo.getBabyUid());
        }
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(uids), Template.getTemplate(810));
        if(toponeInfo!=null){
            AwardAttrConfig award = attr.findAward(toponeInfo.getScore());
            String awardText = award.getNum()+"个"+award.getAwardName()+"（价值"+award.getAwardAmount()+"元)";
            String fly = null;
            if(toponeInfo.getSid()!=null && toponeInfo.getSid()>0){
                fly = String.format("yy://pd-[sid=%d&subid=%d]",toponeInfo.getSid(),toponeInfo.getSsid());
            }

            UserInfoVo userInfo = userInfoVoMap.get(toponeInfo.getUserUid());
            UserInfoVo babyInfo = userInfoVoMap.get(toponeInfo.getBabyUid());
            content.append("本轮第一CP：");
            content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
            content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")\n");
            content.append("缘分值：").append(toponeInfo.getScore()).append("\n");
            content.append("获得奖励：").append(awardText).append("\n");
            content.append("所在房间飞机票：").append(fly).append("\n");
        }

        String totalAwardKey = makeKey(attr,CP_RACE_TOTAL_AWARD_STATISTIC);
        String totalAwardValue = actRedisDao.get(groupCode,totalAwardKey);

        if(StringUtils.isNotEmpty(totalAwardValue)) {
            content.append("全服当前累计发放礼物金额：").append(totalAwardValue).append("元\n");
        }else{
            content.append("全服当前累计发放礼物金额：").append("0元\n");
        }

        String msg = buildActRuliuMsg(actId, false, "鹊桥竞速玩法-半小时", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

    }


}
