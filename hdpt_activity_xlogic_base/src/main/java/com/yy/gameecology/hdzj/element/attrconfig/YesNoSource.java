package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/18 14:32
 **/
@Component("yesNoSource")
public class YesNoSource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // "[{\"code\":\"1\",\"desc\":\"是\"},{\"code\":\"0\",\"desc\":\"否\"}]"
        return Arrays.asList(
                new DropDownVo("0", "否(0)"),
                new DropDownVo("1", "是(1)")
        );
    }
}
