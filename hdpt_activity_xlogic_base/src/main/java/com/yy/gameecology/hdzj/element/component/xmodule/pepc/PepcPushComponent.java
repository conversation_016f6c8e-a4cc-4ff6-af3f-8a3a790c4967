package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.event.AppPopUpEvent;
import com.yy.gameecology.activity.bean.event.JyLayerPushEvent;
import com.yy.gameecology.activity.bean.event.PublicScreenNotifyEvent;
import com.yy.gameecology.activity.bean.mq.ActShareSuccessEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.IMMessageServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.aov.game.AovGameService;
import com.yy.gameecology.activity.service.pepc.PepcGameService;
import com.yy.gameecology.activity.service.pepc.PepcPhaseInfoService;
import com.yy.gameecology.activity.service.pepc.PepcPushService;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.mapper.aov.*;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMemberMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcPhaseSubscribeMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.aov.AovGameInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcActPushComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.*;

@UseRedisStore(NeedChange = true)
@Component
@RestController
@RequestMapping("/5144")
public class PepcPushComponent extends BaseActComponent<PepcActPushComponentAttr> {

    private static final String START_GAME_SMS = "pepc_start_sms:%d:%d:%d:%s";

    private static final String START_GAME_NOTICE = "pepc_start_game:%d:%d:%d:%d";

//    private static final String START_GAME_SMS_SUFFIX = "pepc_start_sms";

    private static final String LAYER_NOTICE_SUFFIX = "pepc_layer_%s:%d:%d:%d";

    @Resource
    private AovPhaseMapper phaseMapper;

    @Resource
    private AovGameExtMapper aovGameExtMapper;

    @Resource
    private PepcTeamMapper pepcTeamMapper;

    @Resource
    private PepcPhaseSubscribeMapper pepcPhaseSubscribeMapper;

    @Resource
    private PepcTeamMemberMapper pepcTeamMemberMapper;

//    @Resource
//    private AovPopupRecordMapper aovPopupRecordMapper;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Autowired
    private PepcGameService pepcGameService;

    @Autowired
    private AovGameService aovGameService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private IMMessageServiceClient imMessageServiceClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private PepcPushService pepcPushService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private PepcPhaseInfoService pepcPhaseInfoService;

    @Autowired
    private CommonDataDao commonDataDao;
    @Autowired
    private PepcGameMemberMapper pepcGameMemberMapper;

    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_PUSH;
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = false)
    public void onZhuiwanLogin(ZhuiwanLoginEvent event, PepcActPushComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());

        PepcPhaseInfo phaseInfo = pepcPhaseInfoService.getSignupPhase(attr.getActId());
        if (!DateUtils.isSameDay(now, phaseInfo.getStartTime())) {
            return;
        }

        if (attr.getPrevActId() == 0) {
            return;
        }

        PepcSubscribe subscribe = pepcPhaseSubscribeMapper.selectUserSubscribe(attr.getPrevActId(), event.getUid());
        if (subscribe == null || subscribe.getState() != 0) {
            return;
        }

        int rs = pepcPhaseSubscribeMapper.updateSubscribeState(subscribe.getId(), 0, 1);
        if (rs <= 0) {
            return;
        }

        // 发
        doSendNotice(attr.getImAppId(), attr.getImSenderUid(), event.getUid(), attr.getImMsgTitle(), attr.getSubscribeMsg(), attr.getImMsgLink());
    }

    // todo 结算通知
//    @HdzjEventHandler(value = HdzkAovRoundSettledEvent.class, canRetry = true)
//    public void onAovRoundSettled(HdzkAovRoundSettledEvent event, PepcActPushComponentAttr attr) {
//        int advancedSize = CollectionUtils.isEmpty(event.getAdvancedTeamIds()) ? 0 : event.getAdvancedTeamIds().size();
//        int eliminatedSize = CollectionUtils.isEmpty(event.getEliminatedTeamIds()) ? 0 : event.getEliminatedTeamIds().size();
//        String msg = buildActRuliuMsg(attr.getActId(), false, "轮次结算成功", "轮次【" + event.getRoundName() + "】结算成功，获胜晋级队伍数：" + advancedSize + "，补位晋级队伍数：" + (eliminatedSize - advancedSize) + "，淘汰队伍数：" + eliminatedSize);
//        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Collections.emptyList());
//    }

    @HdzjEventHandler(value = ActShareSuccessEvent.class, canRetry = true)
    public void onActShareSuccessEvent(ActShareSuccessEvent event, PepcActPushComponentAttr attr) {
        log.info("onActShareSuccessEvent,event:{} attrActId:{}", JSON.toJSONString(event), attr.getActId());
        if (event.getUid() < 0) {
            log.info("uid empty {} {}",JSON.toJSONString(event), attr.getActId());
            return;
        }
        if (event.getUid() == event.getShareUid()) {
            log.info("two users are the same person uid:{} {} {}", event.getUid(),JSON.toJSONString(event), attr.getActId());
            return;
        }
        if (StringUtil.isBlank(attr.getShareInviteUrl())) {
            log.info("not config shareUrl");
            return;
        }
        if (!StringUtil.isJson(event.getExtJson())) {
            log.info("ext is not json,seq:{},actId:{},uid:{},ext:{}", event.getSeq(), event.getUid(), attr.getActId(), event.getExtJson());
            return;
        }
        JSONObject extJson = JSON.parseObject(event.getExtJson());
        Long teamId = extJson.getLong("teamId");
        Long shareActId = extJson.getLong("actId");
        if (teamId == null) {
            log.warn("teamId is null,uid:{} {} {}", event.getUid(),JSON.toJSONString(event), attr.getActId());
            return;
        }
        if (shareActId == null || shareActId == 0) {
            shareActId = attr.getActId();
        }
        PepcTeam team = pepcTeamMapper.selectById(teamId);
        if (team == null) {
            log.warn("team is null,uid:{} {} {}", event.getUid(),JSON.toJSONString(event), attr.getActId());
            return;
        }
        if (shareActId != attr.getActId()) {
            log.warn("not current act share event {} {}",JSON.toJSONString(event), attr.getActId() );
            return;
        }
        // todo 判断时段
//        AovPhase aovPhase = phaseMapper.selectByPhaseId(team.getPhaseId());
//        if (aovPhase == null || !aovPhase.getActId().equals(attr.getActId())) {
//            log.warn("act not match,actId:{},uid:{},aovPhase:{}", attr.getActId(), event.getUid(), JSON.toJSONString(aovPhase));
//            return;
//        }
        Date now = commonService.getNow(team.getActId());
        if (!actInfoService.inActTime(team.getActId())) {
            log.warn("act not match,event:{} actId:{},uid:{}", attr.getActId(), JSON.toJSONString(event),event.getUid());
            return;
        }

        //低版本ios有bug，同1口令1s内会重复上报，这里做个去重
        String dupSeq = makeKey(attr, "onActShareSuccessEventPop:" + event.getUid());
        RetryTool.withRetryCheck(team.getActId(), dupSeq, Const.TEN, () -> {
            String nick = commonService.getNickName(event.getShareUid(), true);
            AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
            appPopUpEvent.setUid(event.getUid());
            String url = attr.getShareInviteUrl()
                    .replace("{shareNick}", nick)
                    .replace("{shareNickUrlEncode}", URLEncoder.encode(nick, StandardCharsets.UTF_8))
                    .replace("{teamId}", Convert.toString(teamId));
            appPopUpEvent.setPopUrl(url);
            appPopUpEvent.setSeq(makeKey(attr, "appSharePop_" + event.getSeq()));
            appPopUpEvent.setProductTime(System.currentTimeMillis());
            kafkaService.sendAppPopUp(appPopUpEvent);
            log.info("onActShareSuccessEvent done,{} {} uid:{},url:{}", event.getUid(), attr.getActId(), JSON.toJSONString(event), url);
        });

    }

    public boolean hasSubscribe(long actId, long uid) {
        return pepcPhaseSubscribeMapper.selectUserSubscribe(actId, uid) != null;
    }

    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(initialDelay = 2000, fixedDelay = 10000)
    public void trySendSubscribePush() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

//            AovPhase phase = phaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INIT);
//            if (phase == null) {
//                continue;
//            }
//
//            if (phase.getPrevPhaseId() <= 0) {
//                continue;
//            }

            PepcPhaseInfo phaseInfo = pepcPhaseInfoService.getSignupPhase(actId);

            if (phaseInfo == null) {
                continue;
            }

            doTrySendSubscribePush(attr, phaseInfo, now);
        }
    }

    private void doTrySendSubscribePush(PepcActPushComponentAttr attr, PepcPhaseInfo phase, Date now) {
        if (!DateUtils.isSameDay(now, phase.getStartTime())) {
            return;
        }

        if (attr.getPrevActId() == 0) {
            return;
        }

        LocalTime curTime = DateUtil.toLocalDateTime(now).toLocalTime();

        if (curTime.isBefore(attr.getStartTime()) || curTime.isAfter(attr.getEndTime())) {
            log.info("NOT IN NOTICE TIME {}",attr.getActId());
            return;
        }

        List<PepcSubscribe> subscribes = pepcPhaseSubscribeMapper.selectSubscribes(attr.getPrevActId(), 0, 50);
        if (CollectionUtils.isEmpty(subscribes)) {
            return;
        }

        for (PepcSubscribe subscribe : subscribes) {
            int rs = pepcPhaseSubscribeMapper.updateSubscribeState(subscribe.getId(), 0, 1);

            if (rs > 0) {
                doSendNotice(attr.getImAppId(), attr.getImSenderUid(), subscribe.getUid(), attr.getImMsgTitle(), attr.getSubscribeMsg(), attr.getImMsgLink());
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), subscribe.getUid(), attr.getSubscribeSms(), attr.getSmsSecret(), attr.getSmsAppid());
                log.info("doTrySendSubscribePush to uid:{}", subscribe.getUid());
            }
        }
    }

    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(initialDelay = 5000, fixedDelay = 10000)
    public void trySendStartGameNotice() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            Date now = commonService.getNow(actId);

//            AovPhase phase = phaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INITIALIZED);
//            if (phase == null) {
//                continue;
//            }

            // 还在报名阶段
//            if (pepcPhaseInfoService.InSignUpTime(actId, now)) {
//                continue;
//            }

            // 分组成功
            List<PepcGame> gameList = pepcGameService.getGameByState(actId, PepcConst.GameState.GAME_CREATED);

            if (CollectionUtils.isEmpty(gameList)) {
                continue;
            }

            log.info("now notice to actId:{}", actId);
//            StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
            for (PepcGame game : gameList) {
                doTrySendStartGameNotice(attr, game, now);
            }
        }
    }


    private String makeNoticeKey(long actId, long comptId, long cmpIndex, long gameId) {
        return String.format(START_GAME_NOTICE, actId, comptId, cmpIndex, gameId);
    }

    private String makeSmsKey(long actId, long comptId, long cmpIndex, Date now) {
        return String.format(START_GAME_SMS, actId, comptId, cmpIndex, DateFormatUtils.format(now, com.yy.gameecology.common.utils.DateUtil.PATTERN_TYPE2));
    }

    private String makeLayerKey(long actId, long comptId, long cmpIndex, long gameId) {
        return String.format(LAYER_NOTICE_SUFFIX, actId, comptId, cmpIndex, gameId);
    }

    private void doTrySendStartGameNotice(PepcActPushComponentAttr attr, PepcGame game, Date now) {
        log.info("doTrySendStartGameNotice start actId:{} game:{} now:{}", attr.getActId(), game, DateUtil.formatDateTime(now));
        if (now.before(DateUtils.addMinutes(game.getStartTime(),attr.getJumpGameMin()))) {
            log.info("doTrySendStartGameNotice not yet begin game:{}", game);
            return;
        }

        List<PepcGameMember> members = pepcGameMemberMapper.select(attr.getActId(), List.of(game.getId()));
        if (CollectionUtils.isEmpty(members)) {
            log.info("doTrySendStartGameNotice no members game:{}", game);
            return;
        }


        String noticeKey = makeNoticeKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), game.getId());
        String smsKey = makeSmsKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), now);

//        boolean set1 = false, set2 = false;
//        Map<Long, List<AovGameInfo>> matchMap = new HashMap<>(2);
        Set<Long> teamMap = new HashSet<>();
        for (PepcGameMember member : members) {
            log.info("doTrySendStartGameNotice member:{}", member);
            long uid = member.getUid();
            boolean put = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), noticeKey, Convert.toString(uid), "1");
            if (!put) {
                log.info("doTrySendStartGameNotice pass {} member:{}", member.getGameId(), member.getUid());
                continue;
            }


            teamMap.add(member.getTeamId());

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getStartGameMsg(), attr.getImMsgLink());
            sendStartGameNotice(attr, uid, game.getId());
//            long teamId = member.getTeamId();
//            List<AovGameInfo> matches = matchMap.get(teamId);
//            matches = sendStartGameGameList(attr, uid, member.getActId(), teamId, now, matches);
//            if (!matchMap.containsKey(teamId)) {
//                matchMap.put(teamId, matches);
//            }
//            sendStartGameGameList(attr, uid, member.getActId(), teamId, now, matches);

//            put = redisTemplate.opsForHash().putIfAbsent(smsKey, String.valueOf(uid), "1");
            put = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), smsKey, Convert.toString(uid), "1");
            if (!put) {
                log.info("doTrySendStartGameNotice sms pass {} member:{}", member.getGameId(), member.getUid());
                continue;
            }

            userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getStartGameSms(), attr.getSmsSecret(), attr.getSmsAppid());

        }

        String LayKey = makeLayerKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), game.getId());
        log.info("doTrySendStartGameNotice teamMap {}", teamMap);
        for (long t : teamMap) {
            boolean layRet = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LayKey, Convert.toString(t), "1");
            if (layRet) {
                log.info("doTrySendStartGameNotice team {}", t);
                threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendTeamLayerShow(attr.getActId(), t));
            }
        }

//        if (set2) {
//        redisTemplate.expire(smsKey, 2, TimeUnit.DAYS);
//        }

    }

    private void sendTeamLayerShow(long actId, long teamId) {
        PepcTeam team = pepcTeamMapper.selectById(teamId);
        if (team == null) {
            return;
        }

        JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
        jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
        jyLayerPushEvent.setProducerTime(System.currentTimeMillis() / 1000);
        jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
        jyLayerPushEvent.setFromService(actId + "-bro");
        jyLayerPushEvent.setFromIP(SystemUtil.getIp());
        jyLayerPushEvent.setSid(team.getSid());
        jyLayerPushEvent.setSsid(team.getSsid());
        jyLayerPushEvent.setStatus(1); //1 -打开 2 -关闭
        jyLayerPushEvent.setActivityID(actId); //
        kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
        log.info("sendTeamLayerShow with teamId:{} sid:{} ssid:{}", teamId, team.getSid(), team.getSsid());
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "30 0 10 * * ?")
    public void sendStatisticNotice() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if(attr == null) {
                continue;
            }
            Date now = commonService.getNow(actId);

            timerSupport.work("sendStatisticNotice:" + actId, 300, () -> {
                log.info("sendStatisticNotice with actId:{}", actId);
                pepcPushService.sendStatisticNotice(attr, now);
            });
        }
    }

    @GetMapping("subscribe")
    public Response<?> subscribe(@RequestParam(name = "actId") long actId,
                                 @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        PepcActPushComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(500, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        // todo 是否有下一期活动

        if (!actInfoService.inActTime(actId)) {
            return Response.ok();
        }

        PepcSubscribe subscribe = pepcPhaseSubscribeMapper.selectUserSubscribe(actId, uid);
        if (subscribe != null) {
            return Response.ok();
        }

        PepcSubscribe record = new PepcSubscribe();
        record.setPrevActId(actId);
        record.setUid(uid);
        record.setState(0);
        record.setCreateTime(new Date());

        pepcPhaseSubscribeMapper.insertSelective(record);

        return Response.ok();
    }


    @GetMapping("statistic")
    public Response<?> getStatisticData(@RequestParam(name = "actId") long actId,
                                        @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {

        PepcActPushComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid != 50013181) {
            return Response.fail(403, "unsupported");
        }

        pepcPushService.sendStatisticNotice(attr, commonService.getNow(actId));
        return Response.ok();
    }

    public void sendRejectedNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendRejectedNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getRejectedMsg(), attr.getImMsgLink());
            log.info("sendRejectedNotice done with uid:{}", uid);
        });
    }

    public void sendNewApplyNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendNewApplyNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getNewApplyMsg(), attr.getApproveLink());
            log.info("sendNewApplyNotice done with uid:{}", uid);
        });
    }

    public void sendApprovedNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendApprovedNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getApprovedMsg(), attr.getImMsgLink());
            userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getApprovedSms(), attr.getSmsSecret(), attr.getSmsAppid());
            log.info("sendApprovedNotice done with uid:{}", uid);
        });
    }

    public void sendKickedOutNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendKickedOutNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getKickedOutMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getKickedOutSms())) {
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getKickedOutSms(), attr.getSmsSecret(), attr.getSmsAppid());
            }

            log.info("sendKickedOutNotice done with uid:{}", uid);
        });
    }

    public void sendDisbandTeamNotice(long actId, List<Long> memberUids) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendDisbandTeamNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), memberUids, attr.getImMsgTitle(), attr.getDisbandMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getDisbandSms())) {
                memberUids.forEach(uid -> userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getKickedOutSms(), attr.getSmsSecret(), attr.getSmsAppid()));
            }

            log.info("sendDisbandTeamNotice done with uid:{}", memberUids);
        });
    }

    public void sendQuitTeamNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            PepcActPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendQuitTeamNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getQuitTeamMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getQuitTeamSms())) {
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getQuitTeamSms(), attr.getSmsSecret(), attr.getSmsAppid());
            }

            log.info("sendQuitTeamNotice done with uid:{}", uid);
        });
    }

    private void doSendNotice(int appid, long senderUid, long receiveUid, String title, String msg, String link) {
        imMessageServiceClient.sendZhuiwanIMPush(appid, senderUid, Collections.singletonList(receiveUid), title, msg, StringUtils.EMPTY, link);
    }

    private void doSendNotice(int appid, long senderUid, List<Long> receiveUids, String title, String msg, String link) {
        imMessageServiceClient.sendZhuiwanIMPush(appid, senderUid, receiveUids, title, msg, StringUtils.EMPTY, link);
    }

    private void sendStartGameNotice(PepcActPushComponentAttr attr, long uid, long gameId) {
        JSONObject json = new JSONObject(3);
        json.put("gameId", gameId);
        String noticeValue = json.toJSONString();

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.PEPC_START_GAME_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("sendStartGameNotice with uid:{} noticeValue:{}", uid, noticeValue);
    }


    /**
     * ugc房间公屏消息通知
     */
    public void sendRoomNotice(long actId, String seq, final String text, long uid, long sid, long ssid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            String textContent = text.replace("{uid}", Convert.toString(uid));

            PublicScreenNotifyEvent event = kafkaService.buildPublicScreenNotifyEvent(actId, seq, textContent, ZhuiwanScreenNotifyType.SUB_CHANNEL, uid, sid, ssid, 0, Lists.newArrayList());
            kafkaService.sendZhuiwanPublicScreenNotifyEvent(event);
        });
    }

    public void sendJoinTeamIm(PepcActPushComponentAttr pushAttr, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            if (pushAttr == null) {
                log.error("sendJoinTeamIm fail component not exist");
                return;
            }
            imMessageServiceClient.sendZhuiwanActIMPush(pushAttr.getImAppId(), pushAttr.getImSenderUid(), Collections.singletonList(uid), pushAttr.getImMsgTitle(), pushAttr.getJoinTeamIm(), StringUtils.EMPTY, pushAttr.getImMsgLink());
            log.info("sendJoinTeamIm done with uid:{}", uid);
        });
    }

    private List<AovGameInfo> sendStartGameGameList(PepcActPushComponentAttr attr, long uid, long phaseId, long teamId, Date now, List<AovGameInfo> matches) {
        if (matches == null) {
            matches = aovGameService.queryMatches(phaseId, teamId, now);
        }
        String noticeValue = JSON.toJSONString(matches);

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.AOV_FRESH_GAME_LIST_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("sendStartGameGameList with uid:{} noticeValue:{}", uid, noticeValue);

        return matches;
    }
}
