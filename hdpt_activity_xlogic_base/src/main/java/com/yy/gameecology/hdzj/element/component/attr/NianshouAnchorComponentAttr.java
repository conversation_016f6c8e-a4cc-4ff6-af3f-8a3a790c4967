package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;

/**
 * @author: yulianzhu
 * @date: 2022.12.26
 * @description:
 */

public class NianshouAnchorComponentAttr extends ComponentAttr {

    //战斗时间, 10分钟
    @ComponentAttrField(labelText = "战斗时长")
    private long fightTime = 600;

    //结果展示时间
    @ComponentAttrField(labelText = "结果展示时长")
    private int showResultTime = 10;

    //当前业务ID
    @ComponentAttrField(labelText = "业务ID")
    private int busiId;

    //主播首次开播tips
    @ComponentAttrField(labelText = "开播tip")
    private String anchorFirstTip = "";

    //监听榜单
    @ComponentAttrField(labelText = "监听打年兽榜单")
    private long nsRankId;

    @ComponentAttrField(labelText = "年兽总血量")
    private long nsTotalBlood;

    @ComponentAttrField(labelText = "年兽礼物ID", remark = "打败后,需上报打败数量")
    private String nsItemId;

    @ComponentAttrField(labelText = "用户角色ID", remark = "用于记录用户的贡献")
    private Long playRoleId;
    //送礼随机抽奖门槛
    @ComponentAttrField(labelText = "随机抽奖门槛")
    private long drawHold;

    @ComponentAttrField(labelText = "打败年兽抽大奖门槛")
    private long bigDrawHold = 1000;

    @ComponentAttrField(labelText = "topN抽大奖资格")
    private int topNBigDraw = 5;

    @ComponentAttrField(labelText = "礼物名称")
    private String giftName;

    @ComponentAttrField(labelText = "创建年兽时间点(分钟)", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)})
    private List<Integer> genNsMinute = Lists.newArrayList(0, 15, 30, 45);

    @ComponentAttrField(labelText = "创建年兽时长发送如流消息(毫秒)")
    private long creTimeHold = 0;

    public List<Integer> getGenNsMinute() {
        return genNsMinute;
    }

    public void setGenNsMinute(List<Integer> genNsMinute) {
        this.genNsMinute = genNsMinute;
    }

    public int getTopNBigDraw() {
        return topNBigDraw;
    }

    public void setTopNBigDraw(int topNBigDraw) {
        this.topNBigDraw = topNBigDraw;
    }

    public long getBigDrawHold() {
        return bigDrawHold;
    }

    public void setBigDrawHold(long bigDrawHold) {
        this.bigDrawHold = bigDrawHold;
    }

    public long getNsRankId() {
        return nsRankId;
    }

    public void setNsRankId(long nsRankId) {
        this.nsRankId = nsRankId;
    }

    public long getNsTotalBlood() {
        return nsTotalBlood;
    }

    public void setNsTotalBlood(long nsTotalBlood) {
        this.nsTotalBlood = nsTotalBlood;
    }

    public String getNsItemId() {
        return nsItemId;
    }

    public void setNsItemId(String nsItemId) {
        this.nsItemId = nsItemId;
    }

    public Long getPlayRoleId() {
        return playRoleId;
    }

    public void setPlayRoleId(Long playRoleId) {
        this.playRoleId = playRoleId;
    }

    public long getFightTime() {
        return fightTime;
    }

    public void setFightTime(long fightTime) {
        this.fightTime = fightTime;
    }

    public int getShowResultTime() {
        return showResultTime;
    }

    public void setShowResultTime(int showResultTime) {
        this.showResultTime = showResultTime;
    }

    public int getBusiId() {
        return busiId;
    }

    public void setBusiId(int busiId) {
        this.busiId = busiId;
    }

    public String getAnchorFirstTip() {
        return anchorFirstTip;
    }

    public void setAnchorFirstTip(String anchorFirstTip) {
        this.anchorFirstTip = anchorFirstTip;
    }

    public long getDrawHold() {
        return drawHold;
    }

    public void setDrawHold(long drawHold) {
        this.drawHold = drawHold;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public long getCreTimeHold() {
        return creTimeHold;
    }

    public void setCreTimeHold(long creTimeHold) {
        this.creTimeHold = creTimeHold;
    }
}
