package com.yy.gameecology.hdzj.element.event;

import lombok.Data;

/**
 * <AUTHOR> 2024/7/12
 */
@Data
public class RechargeActRewardEvent {
    
    /**
     * seqId
     */
    private String producerSeqID;
    
    /**
     * 秒
     */
    private long producerTime;
    
    /**
     * 活动类型: [1-大额充值享豪礼 2-充值福利大派送 3-拼团充值狂欢节 4-拼团充值嘉年华]
     */
    private int actType;
    
    /**
     * 活动id
     */
    private String actId;
    
    /**
     * 活动名
     */
    private String actName;
    
    /**
     * 领奖uid
     */
    private long uid;
    
    /**
     * 发放时间, 毫秒
     */
    private long rewardTime;
    
    /**
     * 货币类型 [紫水晶-2、紫金币-31、布料-33、Y币-3、能量-34]
     */
    private int priceType;
    
    /**
     * 返奖比，例如 10%
     */
    private String backRate;
    
    /**
     * 充值金额/兑换积分
     */
    private long chargeAmount;
    
    /**
     * 发放的货币数量
     */
    private long rewardAmount;
    
    /**
     * 虚拟奖励发放内容描述，例如"7天豪标、7天头像框"
     */
    private String contentDesc;
    
}
