package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class SweetFerrisWheelComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "基础榜单ID", remark = "统计CP荣耀值的基础榜单ID")
    protected long baseRankId;

    @ComponentAttrField(labelText = "每日基础榜单ID", remark = "统计每日荣耀值的基础榜单ID")
    protected long baseDailyRankId;

    @ComponentAttrField(labelText = "基础阶段ID", remark = "统计CP荣耀值的基础阶段ID")
    protected long basePhaseId;

    @ComponentAttrField(labelText = "基础星星分", remark = "获得一颗星星所需要的分（荣耀值）")
    protected long baseScore;

    @ComponentAttrField(labelText = "每日榜单ID", remark = "每日星星榜的榜单ID")
    protected long dailyRankId;

    @ComponentAttrField(labelText = "每日阶段ID", remark = "每日星星榜的阶段ID")
    protected long dailyPhaseId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    protected long timeKey;

    @ComponentAttrField(labelText = "CP信息组件索引")
    protected long cpInfoComponentIndex;

    @ComponentAttrField(labelText = "最后CP组件索引")
    protected long latestCpIndex;

    @ComponentAttrField(labelText = "全服广播等级", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "点亮星星的任务中，需要全服广播的等级，多个英文逗号隔开")
    protected Set<Long> dailyTaskBroLevels;

    @ComponentAttrField(labelText = "口令抽奖等级", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "点亮星星的任务中，需要触发口令抽奖的等级，多个英文逗号隔开")
    protected Set<Long> watchwordLotteryLevels;

    @ComponentAttrField(labelText = "口令抽奖组件索引")
    protected long watchwordLotteryIndex;

    @ComponentAttrField(labelText = "奖励上限组件索引")
    protected long awardLimitIndex;

    @ComponentAttrField(labelText = "口令抽奖时长")
    protected Duration watchwordLotteryDuration;

    @ComponentAttrField(labelText = "每日星星任务奖励名称", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级", remark = "任务等级，也就是点亮的星星的数量"),
            @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "奖励名称", remark = "任务等级对应的奖励名称")
    }, remark = "点亮每颗星星的奖励名称")
    protected Map<Long, String> dailyTaskAwards;

    @ComponentAttrField(labelText = "星星榜单ID")
    protected long totalRankId;

    @ComponentAttrField(labelText = "星星阶段ID")
    protected long totalPhaseId;

    @ComponentAttrField(labelText = "奖励单播延迟")
    protected Duration noticeDelay;

    @ComponentAttrField(labelText = "累计奖励配置", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级", remark = "累计任务对应的等级，从1开始"),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = AwardAttrConfig.class)
    })
    protected Map<Long, List<AwardAttrConfig>> totalTaskAwards;

    @ComponentAttrField(labelText = "累计备用奖励", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = AwardAttrConfig.class), remark = "累计奖励达到上限后发的备用奖励")
    protected List<AwardAttrConfig> totalTaskSpareAwards;

    @ComponentAttrField(labelText = "mp4特效url")
    protected String mp4Url;

    @ComponentAttrField(labelText = "优先级", remark = "特效排队显示优先级，值越小，优先级越高；目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
    protected int mp4Level;

    @ComponentAttrField(labelText = "app特效key配置", remark = "mp4中的key配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "key"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "value", remark = "可以包含占位符的图片或富文本")
            })
    protected Map<String, String> mp4LayerExtKeyValues;
}
