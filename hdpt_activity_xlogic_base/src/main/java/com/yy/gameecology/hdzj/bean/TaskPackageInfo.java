package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-09-08 10:00
 **/
public class TaskPackageInfo {
    @ComponentAttrField(labelText = "奖品名称")
    private String name;
    @ComponentAttrField(labelText = "奖池id")
    private long taskId;
    @ComponentAttrField(labelText = "奖包id")
    private long packageId;

    @ComponentAttrField(labelText = "奖品数量")
    private int count;
    @ComponentAttrField(labelText = "奖品数量")
    private String logo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public long getPackageId() {
        return packageId;
    }

    public void setPackageId(long packageId) {
        this.packageId = packageId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }
}
