package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.rank.PkRankItemBase;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.AnchorBonusPKComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021.08.30 15:32
 */
@Deprecated
@Component
public class AnchorBonusPkComponent extends BaseActComponent<AnchorBonusPKComponentAttr> implements
        ComponentRankingExtHandle<AnchorBonusPKComponentAttr> {

    private static final String ONLY_EXECUTE_KEY = "only_execute";

    private static final String ANCHOR_AWARD_KEY = "anchor_pk_award";

    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_BONUS_PK;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onAnchorPkEnven(PhaseTimeEnd event, AnchorBonusPKComponentAttr attr) {

        long rankId = attr.getRankId();
        if (event.getActId() != attr.getActId() || event.getRankId() != rankId) {
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(event.getActId());

        //防止重复执行
        long phaseId = event.getPhaseId();
        if (!actRedisDao.setNX(groupCode, makeKey(attr, ONLY_EXECUTE_KEY + phaseId), DateUtil.getNowYyyyMMddHHmmss())) {
            return;
        }
        List<String> keys = new ArrayList<>();
        List<Object> rows = new ArrayList<>();
        List<Long> incs = new ArrayList<>();
        String awardKey = Const.addActivityPrefix(attr.getActId(), ANCHOR_AWARD_KEY + ":" + attr.getRankId());
        //初始晋级人数
        if (phaseId == attr.getPkBasePhase()) {
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), rankId, attr.getPkBasePhase(), "", attr.getPkBaseCount(), Maps.newHashMap());
            for (Rank rank : ranks) {
                Long addAwardNum = addAwardNum(phaseId, rank.getRank() <= attr.getPkBaseCount(), attr);
                if (addAwardNum != 0) {
                    keys.add(awardKey);
                    rows.add(rank.getMember());
                    incs.add(addAwardNum);
                }
            }
        } else {
            PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(attr.getActId(), rankId, phaseId, "00000000", "", true, true, Maps.newHashMap());
            pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems).flatMap(Collection::stream).forEach(
                    memberPkItems -> {
                        GroupMemberItem memberItem1 = memberPkItems.get(0);
                        GroupMemberItem memberItem2 = memberPkItems.get(1);
                        boolean memberItem1isWinner = memberItem1.getRank() < memberItem2.getRank();
                        keys.add(awardKey);
                        rows.add(memberItem1.getMemberId());
                        incs.add(addAwardNum(phaseId, memberItem1isWinner, attr));

                        keys.add(awardKey);
                        rows.add(memberItem2.getMemberId());
                        incs.add(addAwardNum(phaseId, !memberItem1isWinner, attr));
                    }
            );
        }

        log.info("onAnchorPkEvent info ranId ={},phaseId ={} row={},incs={}", rankId, phaseId, JSON.toJSONString(rows), JSON.toJSONString(incs));

        if (!keys.isEmpty()) {
            actRedisDao.zBatchIncr(groupCode, keys, rows, incs);
        }
        Set<ZSetOperations.TypedTuple<String>> anchorAwards = actRedisDao.zrange(groupCode, awardKey, 1000);
        String message = anchorAwards.stream().map(info -> info.getValue() + ":" + info.getScore().intValue())
                .collect(Collectors.joining("\n"));
        log.info("onAnchorPkEvent current award info ranId ={},phaseId ={} message={}", rankId, phaseId, message);
    }

    public Long addAwardNum(Long phase, boolean isWinner, AnchorBonusPKComponentAttr attr) {
        //初始晋级,超过不得赏金
        if (phase == attr.getPkBasePhase() && !isWinner) {
            return 0L;
        }
        Long addScore = attr.getPhaseAwardMap().get(phase);
        return isWinner ? addScore : -addScore;
    }

    /**
     * http  读取接口
     *
     * @return
     */
    public Set<ZSetOperations.TypedTuple<String>> queryAllBonusRecord(long actId, long rankId) {
        String awardKey = Const.addActivityPrefix(actId, ANCHOR_AWARD_KEY + ":" + rankId);
        String groupCode = redisConfigManager.getGroupCode(actId);
        return actRedisDao.zrevRange(groupCode, awardKey, 0, -1);
    }

    @Override
    public List<Object> handleExt(AnchorBonusPKComponentAttr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        if (attr.getActId() != rankReq.getActId() || attr.getRankId() != rankReq.getRankId()) {
            return objectList;
        }

        Map<Long, Long> phaseAwardMap = attr.getPhaseAwardMap();

        Long preAdd = phaseAwardMap.getOrDefault(rankReq.getPhaseId(), 0L);
        final long base;
        if (attr.getPkBasePhase() == rankReq.getPhaseId()) {
            base = preAdd;
        } else {
            base = preAdd * 2;
        }

        for (Object obj : objectList) {
            if (obj instanceof PkRankItemBase) {
                PkRankItemBase itemBase = (PkRankItemBase) obj;
                itemBase.setBase(base);
            }
        }
        //第一阶段是晋级
        if (attr.getPkBasePhase() == rankReq.getPhaseId()) {
            return objectList;
        }

        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(attr.getActId(), rankReq.getRankId(), rankReq.getPhaseId(), "00000000", "", true, true, Maps.newHashMap());
        Map<String, Long> preAdMap = Maps.newHashMap();

        pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems).flatMap(Collection::stream).forEach(
                memberPkItems -> {
                    GroupMemberItem memberItem1 = memberPkItems.get(0);
                    GroupMemberItem memberItem2 = memberPkItems.get(1);
                    boolean memberItem1isWinner = memberItem1.getRank() < memberItem2.getRank();
                    preAdMap.put(memberItem1.getMemberId(), memberItem1isWinner ? preAdd : -1 * preAdd);
                    preAdMap.put(memberItem2.getMemberId(), memberItem1isWinner ? -1 * preAdd : preAdd);
                });
        for (Object obj : objectList) {
            if (obj instanceof PkRankItemBase) {
                PkRankItemBase itemBase = (PkRankItemBase) obj;
                itemBase.setAddition(preAdMap.getOrDefault(itemBase.getKey(), 0L));
            }
        }

        return objectList;
    }

}

