package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;

public class TaskCompletePopUpComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "是否推送PC弹窗")
    private boolean broPcBanner;

    @ComponentAttrField(labelText = "是否推送APP弹窗")
    private boolean broAppBanner;

    @ComponentAttrField(labelText = "过任务的榜单")
    private long taskRankId;

    @ComponentAttrField(labelText = "过任务的阶段")
    private long taskPhaseId;

    @ComponentAttrField(labelText = "弹窗url,（标题内容通过url参数透传,需要跟前端对齐）")
    private String popUrl;

    public boolean isBroPcBanner() {
        return broPcBanner;
    }

    public void setBroPcBanner(boolean broPcBanner) {
        this.broPcBanner = broPcBanner;
    }

    public boolean isBroAppBanner() {
        return broAppBanner;
    }

    public void setBroAppBanner(boolean broAppBanner) {
        this.broAppBanner = broAppBanner;
    }

    public long getTaskRankId() {
        return taskRankId;
    }

    public void setTaskRankId(long taskRankId) {
        this.taskRankId = taskRankId;
    }

    public long getTaskPhaseId() {
        return taskPhaseId;
    }

    public void setTaskPhaseId(long taskPhaseId) {
        this.taskPhaseId = taskPhaseId;
    }

    public String getPopUrl() {
        return popUrl;
    }

    public void setPopUrl(String popUrl) {
        this.popUrl = popUrl;
    }
}
