package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SendGiftComponentAttr;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc:送礼发奖组件。送礼的价值，取模发奖
 *
 * @createBy 曾文帜
 * @create 2021-09-14 16:47
 **/
@Component
public class SendGiftComponent extends BaseActComponent<SendGiftComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;
    private static final int AWARD_SENDER = 1;
    private static final int AWARD_RECEIVER = 2;
    private static final int AWARD_BOTH = 3;


    @Override
    public Long getComponentId() {
        return ComponentId.SEND_GIFT_AWARD;
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void sendGiftLottery(SendGiftEvent event, SendGiftComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            //不在时间内
            log.info("sendGiftLottery info not in activity time event = {}", JSON.toJSONString(event));
            return;
        }

        String giftId = event.getGiftId();
        Date now = commonService.getNow(attr.getActId());
        //非指定业务以及礼物
        BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(event.getTemplate());
        if (attr.getBusiId() != busiId.getValue() || !attr.getGiftValueMap().containsKey(event.getGiftId())) {
            return;
        }

        Integer giftNum = event.getGiftNum().intValue();
        String seq = event.getSeq();
        Integer awardAmount = attr.getGiftValueMap().get(event.getGiftId()) * giftNum;

        Map<Long, Map<Long, Integer>> awardTaskPackageIds = Maps.newHashMap();

        Map<Long, Map<Long, Integer>> configTaskPackageIds = attr.getTaskIdPackageId();
        for (Long taskId : configTaskPackageIds.keySet()) {
            Map<Long, Integer> packageIdAmount = configTaskPackageIds.get(taskId);
            for (Long packageId : packageIdAmount.keySet()) {
                Integer packageAmount = packageIdAmount.get(packageId);
                packageAmount = packageAmount * awardAmount;
                packageIdAmount.put(packageId, packageAmount);
            }
            awardTaskPackageIds.put(taskId, packageIdAmount);
        }

        List<Long> awardUidList = new ArrayList<>();
        if (attr.getAwardTarget() == AWARD_SENDER) {
            awardUidList.add(event.getSendUid());
        } else if (attr.getAwardTarget() == AWARD_RECEIVER) {
            awardUidList.add(event.getRecvUid());
        } else if (attr.getAwardTarget() == AWARD_BOTH) {
            awardUidList.add(event.getSendUid());
            awardUidList.add(event.getRecvUid());
        } else {
            throw new SuperException("not support award target:" + attr.getAwardTarget(), 500);
        }

        for (Long userUid : awardUidList) {
            String awardSeq = makeKey(attr, seq + userUid);
            log.info("sendGiftLottery,uid:{},seq:{},awardInfo:{}", userUid, awardSeq, JSON.toJSONString(awardTaskPackageIds));
            BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(awardSeq, userUid, awardTaskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), attr.getRetry(), Collections.emptyMap());
            log.info("sendGiftLottery done,uid:{},seq:{},awardInfo:{},result:{}", userUid, awardSeq, JSON.toJSONString(awardTaskPackageIds), JSON.toJSONString(result));
        }
    }
}
