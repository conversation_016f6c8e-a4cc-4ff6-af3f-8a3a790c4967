package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.bean.event.ActRiskReCheckEvent;
import com.yy.gameecology.activity.bean.event.AppPopUpEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.ActShareSuccessEvent;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.client.yrpc.IMMessageServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.consts.ActStatus;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5156UserInviteMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5156UserInvite;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5156UserInviteLog;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovPhaseComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.InviteTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.InviteTaskDao;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-10 20:58
 **/
@RestController
@RequestMapping("/5156")
public class InviteTaskComponent extends BaseActComponent<InviteTaskComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private InviteTaskDao inviteTaskDao;

    @Autowired
    private TransactionTemplate transactionTemplate;


    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private IMMessageServiceClient imMessageServiceClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private Cmpt5156UserInviteMapper cmpt5156UserInviteMapper;

    @Autowired
    private Locker locker;

    private static final String RISK_RECHECK_PARA_ID = "inviteLogId";
    private static final String RISK_RECHECK_ACT_ID = "actId";


    private static final String REPORT_TEMPLATE = """
            【活动情况跟踪】
            邀请成功参与用户数：${userAmount}
            被邀请用户数：${invitedAmount}
            
            邀请达上限用户数：${userLimitAmount}
            邀请达上限被邀请用户数：${invitedLimitAmount}
            
            产生金币数：${awardAmount}
            未兑换金币数：${notDrawAmount}
            
            ---------------------------------------------
            
            【小时情况跟踪】
            时间范围：${hourRange}
            参与用户数：${hourUserAmount}
            被邀请用户数：${hourInvitedAmount}
            
            邀请达上限用户数：${hourUserLimitAmount}
            邀请达上限被邀请用户数：${hourInvitedLimitAmount}
            
            产生金币数：${hourAwardAmount}
            未兑换金币数：${hourNotDrawAmount}
            
            ---------------------------------------------
            """;

    @Override
    public Long getComponentId() {
        return ComponentId.INVITE_TASK;
    }

    @Scheduled(cron = "0 0 0/1 * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void ruliuReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            ActivityInfoVo actInfo = actInfoService.queryActivityInfo(actId);
            Date now = commonService.getNow(actId);
            if (!actInfoService.inActShowTime(now, actInfo)) {
                continue;
            }
            InviteTaskComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("ruliuReport:" + actId, 60, () -> {
                rlReport(attr.getActId(), attr.getCmptUseInx());
                SysEvHelper.waiting(1000);
            });
        }
    }

    /**
     * 接受分享口令上报
     */
    @HdzjEventHandler(value = ActShareSuccessEvent.class, canRetry = true)
    public void onActShareSuccessEvent(ActShareSuccessEvent event, InviteTaskComponentAttr attr) {
        acceptInviteAction(event, attr, false);
    }

    /**
     * 二次挑战结果处理
     */
    @HdzjEventHandler(value = ActRiskReCheckEvent.class, canRetry = true)
    public void onActRiskReCheckEvent(ActRiskReCheckEvent event, InviteTaskComponentAttr attr) {
        log.info("onActRiskReCheckEvent,event:{},attr:{}", JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (!event.getAppChallengeRsp().isCheck()) {
            log.info("recheck not pass,actId:{},uid:{}", attr.getActId(), event.getAppChallengeRsp().getUid());
            return;
        }
        String extJson = event.getAppChallengeRsp().getParam();
        if (StringUtil.isBlank(extJson)) {
            log.info("ext json is empty");
            return;
        }
        JSONObject jsonObject = JSON.parseObject(extJson);
        long inviteLogId = jsonObject.getLongValue(RISK_RECHECK_PARA_ID);
        long actId = jsonObject.getLongValue(RISK_RECHECK_ACT_ID);
        if (actId != attr.getActId()) {
            log.info("not my duty");
            return;
        }
        if (inviteLogId <= 0) {
            log.info("inviteLogId empty");
            return;
        }
        Cmpt5156UserInviteLog logItem = inviteTaskDao.getCmptUserInviteLog(attr.getActId(), inviteLogId);
        if (logItem == null || StringUtil.isBlank(logItem.getExtJson())) {
            log.info("inviteLog empty,inviteLogId:{}", inviteLogId);
            return;
        }
        ActShareSuccessEvent actShareSuccessEvent = JSON.parseObject(logItem.getExtJson(), ActShareSuccessEvent.class);
        acceptInviteAction(actShareSuccessEvent, attr, true);
    }

    /**
     * 金币领取
     */
    @GetMapping("/receive")
    public Response<String> receive(@RequestParam(name = "actId") long actId,
                                    @RequestParam(name = "cmptIndex") long cmptInx,
                                    @RequestParam(name = "recordIds") String recordIds) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        if (StringUtil.isBlank(recordIds)) {
            return Response.fail(-2, "参数错误");
        }
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-3, "活动未配置");
        }
        String idHash = userinfoThriftClient.getIdHash(uid);
        if (StringUtil.isBlank(idHash)) {
            return Response.fail(-4, "请先完成实名认证");
        }
        List<Long> ids = Arrays.stream(recordIds.split(",")).toList().stream().map(com.yy.gameecology.common.utils.Convert::toLong).toList();
        try {
            for (Long id : ids) {
                log.info("receiveCoin begin,uid:{},id:{}", uid, id);
                receiveCoin(attr, uid, id);
                log.info("receiveCoin done,uid:{},id:{}", uid, id);
            }
        } catch (SuperException e) {
            log.warn("receive fail,uid:{},recordIds:{},msg:{}", uid, recordIds, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("receive error,uid:{},recordIds:{},msg:{}", uid, recordIds, e.getMessage(), e);
            return Response.fail(999, "网络超时，请稍后重试");
        }
        return Response.ok();

    }

    @RequestMapping("/pendIngCoin")
    public Response<PendingCoinRsp> pendIngCoin(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptIndex") long cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-3, "活动未配置");
        }
        PendingCoinRsp rsp = new PendingCoinRsp();
        List<InviteItem> pendingList = queryCmpt5156UserInviteList(actId, cmptInx, uid, ReceiveState.PENDING);
        rsp.setPendingList(pendingList);
        return Response.success(rsp);
    }

    @RequestMapping("/inviteList")
    public Response<InviteListRsp> inviteList(@RequestParam(name = "actId") long actId,
                                              @RequestParam(name = "cmptIndex") long cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-3, "活动未配置");
        }
        InviteListRsp rsp = new InviteListRsp();
        List<InviteItem> list = queryCmpt5156UserInviteList(actId, cmptInx, uid, null);
        rsp.setInviteList(list);
        return Response.success(rsp);
    }

    @RequestMapping("/userStateInfo")
    public Response<UserStateRsp> userStateInfo(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptIndex") long cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-3, "活动未配置");
        }
        UserStateRsp rsp = new UserStateRsp();
        long amount = inviteTaskDao.countUserInviteByUid(actId, cmptInx, uid);
        rsp.setInvited(amount > 0 ? 1 : 0);

        long balance = currencyClient.balance(uid, attr.getCurrencyBusiId(), attr.getCurrencyCid());
        rsp.setBalance(balance);

        var userInfo = commonService.getUserInfo(uid, false);
        if (userInfo != null) {
            rsp.setHeader(userInfo.getHdLogo());
        }

        return Response.success(rsp);

    }

    @RequestMapping("/sendRecheckTest")
    public Response<String> sendRecheckTest(@RequestParam(name = "actId") long actId,
                                            @RequestParam(name = "cmptIndex") long cmptInx,
                                            @RequestParam(name = "uid") long uid,
                                            @RequestParam(name = "logId") long logId,
                                            @RequestParam(name = "hdid") String hdid, HttpServletRequest request) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(-1, "not test env");
        }
        String ipAddress = request.getHeader("X-Forwarded-For");

        if (ipAddress == null || ipAddress.isEmpty()) {
            ipAddress = request.getRemoteAddr();
        }
        var attr = getComponentAttr(actId, cmptInx);
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.toClient(hdid, ipAddress, "yomi", 0, "", "");
        var riskExt = buildHelpRiskExt(attr, uid);
        ZhuiwanRiskClient.RiskCheckResult riskRsp = zhuiwanRiskClient.riskCheck(client, attr.getRiskStrategyKey(), uid, "", "", "", riskExt);

        //触发二次挑战
        if (StringUtil.isNotBlank(riskRsp.getRecheckId())) {

            String para = JsonUtil.toJson(ImmutableMap.of(RISK_RECHECK_PARA_ID, logId, RISK_RECHECK_ACT_ID, attr.getActId()));
            sendRiskRecheck("yomi", attr.getActId(), uid, attr.getRiskStrategyKey(), riskRsp.getRecheckCode(),
                    attr.getRecheckMsg(), attr.getRecheckTitle(), riskRsp.getRecheckId(), para);
            log.info("sendRecheckTest hit recheck,actId:{},uid:{}", attr.getActId(), uid);
        }
        return Response.success(riskRsp.toString());
    }

    @RequestMapping(value = "/inviteLogList")
    public Map<String, Object> inviteLogList(Long actId, Long uid, Long invitedUid, Integer limit) {
        log.info("inviteLogList actId:{},uid:{},invitedUid:{},limit:{}", actId, uid, invitedUid, limit);
        Map<String, Object> result = Maps.newHashMap();
        result.put("code", 0);
        result.put("msg", "hdzk-" + SysEvHelper.getGroup());
        result.put("data", queryInviteLogList(actId, uid, invitedUid, limit));
        return result;
    }


    private void receiveCoin(InviteTaskComponentAttr attr, long uid, long recordId) {
        long actId = attr.getActId();
        long cmptInx = attr.getCmptUseInx();
        Cmpt5156UserInvite invite = inviteTaskDao.getUserInviteById(actId, cmptInx, uid, recordId);
        if (invite == null) {
            throw new SuperException("记录不存在", SuperException.E_DATA_ERROR);
        }
        if (invite.getAwardAmount() <= 0) {
            throw new SuperException("无待领取奖励", SuperException.E_DATA_ERROR);
        }
        if (ReceiveState.PENDING != invite.getAwardReceiveState()) {
            throw new SuperException("非待领取奖励", SuperException.E_DATA_ERROR);
        }

        //TODO 验证事务是否有效
        transactionTemplate.execute((status) -> {

            long updateRet = inviteTaskDao.updateAwardReceiveState(recordId, actId, cmptInx, uid, ReceiveState.PENDING, ReceiveState.RECEIVED);
            if (updateRet <= 0) {
                log.warn("receiveCoin receive state error,id:{},uid:{},updateRet:{}", recordId, uid, updateRet);
                return null;
            }
            String seq = makeKey(attr, "receiveCoin:" + recordId);
            String seqMd5 = MD5SHAUtil.getMD5(seq);
            BatchWelfareResult anchorResult
                    = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getAwardBusiId(),
                    uid, attr.getAwardTaskId(), Convert.toInt(invite.getAwardAmount()), attr.getAwardPackageId(), seqMd5, Maps.newHashMap(), 2);
            if (anchorResult == null || anchorResult.getCode() != 0) {
                log.error("doWelfare error,actId:{},uid:{},result:{}", actId, uid, anchorResult);
                //TODO 验证事务回滚
                status.setRollbackOnly();
                return null;
            }
            log.info("doWelfare done,actId:{},uid:{},packageId:{},amount:{},seq:{},md5Seq:{}", actId, uid, attr.getAwardPackageId(), invite.getAwardAmount(), seq, seqMd5);
            return anchorResult;
        });

    }


    private void acceptInviteAction(ActShareSuccessEvent event, InviteTaskComponentAttr attr, boolean skipRiskRecheck) {
        //口令有效性判断
        log.info("acceptInviteAction,event:{} attrActId:{}", JSON.toJSONString(event), attr.getActId());
        boolean effect = effectEvent(event, attr);
        if (!effect) {
            return;
        }

        String shareHdid = queryShareLastHdid(event.getShareUid(), event.getApp());

        //风控拦截判断
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.toClient(event.getHdid(), event.getIp(), event.getApp(), 0, "", "");
        var riskExt = buildHelpRiskExt(attr, event.getShareUid());
        ZhuiwanRiskClient.RiskCheckResult riskRsp = zhuiwanRiskClient.riskCheck(client, attr.getRiskStrategyKey(), event.getUid(), "", "", "", riskExt);
        if (riskRsp.isForbid()) {
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.RISK, "风控拦截:" + riskRsp.getMsg(), "");
            log.info("acceptInviteAction hit risk,actId:{},uid:{},inviteUid:{}", attr.getActId(), event.getShareUid(), event.getUid());
            return;
        }

        //触发二次挑战
        if (!skipRiskRecheck && StringUtil.isNotBlank(riskRsp.getRecheckId())) {
            long logId = inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.RISK_RECHECK, "二次挑战:" + riskRsp.getMsg(), JSON.toJSONString(event));
            String para = JsonUtil.toJson(ImmutableMap.of(RISK_RECHECK_PARA_ID, logId, RISK_RECHECK_ACT_ID, attr.getActId()));
            sendRiskRecheck(event.getApp(), attr.getActId(), event.getUid(), attr.getRiskStrategyKey(), riskRsp.getRecheckCode(),
                    attr.getRecheckMsg(), attr.getRecheckTitle(), riskRsp.getRecheckId(), para);
            log.info("acceptInviteAction hit recheck,actId:{},uid:{},inviteUid:{}", attr.getActId(), event.getShareUid(), event.getUid());
            return;
        }


        //新老用户设备判断
        long minLoginTime = System.currentTimeMillis() - attr.getNewUserOffsetMill();
        var userLoginTime = getLoginTime(event.getUid(), event.getHdid(), event.getApp(), false);
        long uidFirstLoginTime = userLoginTime.getResult().getUidFirstLoginTime();
        if (uidFirstLoginTime > 0 && uidFirstLoginTime < minLoginTime) {
            String time = DateUtil.format(new Date(uidFirstLoginTime));
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.OLD_USER, "老uid,首次登陆时间:" + time, "");
            doSendNotice(attr, event.getUid(), "您已是Yo语音用户，无法被他人邀请");
            log.info("acceptInviteAction old user,actId:{},uid:{},inviteUid:{},time:{}", attr.getActId(), event.getShareUid(), event.getUid(), time);
            return;
        }
        long hdidFirstLoginTime = userLoginTime.getResult().getDeviceFirstLoginTime();
        if (hdidFirstLoginTime > 0 && hdidFirstLoginTime < minLoginTime) {
            String time = DateUtil.format(new Date(hdidFirstLoginTime));
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.OLD_HDID, "老设备,首次登陆时间:" + time, "");
            doSendNotice(attr, event.getUid(), "您已是Yo语音用户，无法被他人邀请");
            log.info("acceptInviteAction old hdid,actId:{},uid:{},inviteUid:{},time:{}", attr.getActId(), event.getShareUid(), event.getUid(), time);
            return;
        }

        //uid被占用判断
        Cmpt5156UserInvite invite = inviteTaskDao.getUserInviteByInvitedUid(attr.getActId(), attr.getCmptUseInx(), event.getUid());
        if (invite != null) {
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.DUP_UID, "用户已被邀请,邀请人:" + invite.getUid(), "");
            doSendNotice(attr, event.getUid(), "您已经接受过邀请，无法重复操作");
            log.info("acceptInviteAction dup uid,actId:{},uid:{},inviteUid:{},firstUid:{}", attr.getActId(), event.getShareUid(), event.getUid(), invite.getUid());
            return;
        }


        //用户设备被占用判断
        Cmpt5156UserInvite hdInvite = inviteTaskDao.getUserInviteByInvitedHdid(attr.getActId(), attr.getCmptUseInx(), event.getHdid());
        if (hdInvite != null) {
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.MOBILE_NOT_BIND, "设备已被邀请,邀请人:" + hdInvite.getUid(), "");
            doSendNotice(attr, event.getUid(), "您已经接受过邀请，无法重复操作");
            log.info("acceptInviteAction dup uid,actId:{},uid:{},inviteUid:{},firstUid:{}", attr.getActId(), event.getShareUid(), event.getUid(), hdInvite.getUid());
            return;
        }

        String useMobileHash = userinfoThriftClient.getMobileHash(event.getShareUid());
        if (StringUtil.isBlank(useMobileHash)) {
            inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                    event.getUid(), event.getHdid(), InviteFailedState.DUP_HDID, "未绑定手机号,uid:" + event.getShareUid(), "");
            log.info("acceptInviteAction dup uid,actId:{},uid:{},inviteUid:{}", attr.getActId(), event.getShareUid(), event.getUid());
            return;
        }

        //邀请人 uid-手机号-设备唯一性 判断
        var bindInfo = inviteTaskDao.getCmptUserInviteBind(attr.getActId(), event.getShareUid());
        if (bindInfo != null) {
            boolean hdidChange = !shareHdid.equals(bindInfo.getHdid());
            boolean mobileChange = !useMobileHash.equals(bindInfo.getMobileHash());
            if (hdidChange || mobileChange) {
                inviteTaskDao.addUserInviteLog(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), shareHdid,
                        event.getUid(), event.getHdid(), InviteFailedState.BIND_INFO_CHANGE, "手机号、设备号设备绑定信息变化,uid:" + event.getShareUid(), "");
                log.info("acceptInviteAction bind change uid,actId:{},uid:{},inviteUid:{},shareHdid:{},useMobileHash:{}"
                        , attr.getActId(), event.getShareUid(), event.getUid(), shareHdid, useMobileHash);
                return;
            }
        }


        //保存邀请信息
        String lockKey = makeKey(attr, "saveUserInvite:" + event.getShareUid());
        Secret secret = locker.lock(lockKey, 10, "", 10, 100);
        if (secret == null) {
            throw new RuntimeException("lock fail,auto retry later");
        }
        long awardAmount;
        try {
            awardAmount = saveInviteData(event, attr);
            if (bindInfo == null) {
                long bindRet = inviteTaskDao.inertIgnoreUserInviteBind(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), useMobileHash, shareHdid);
                log.info("save bindInfo,actId:{},uid:{},bindRet:{}", attr.getActId(), event.getShareUid(), bindRet);
            }

        } catch (Exception e) {
            log.error("saveInviteData fail,auto retry later,e:{}", e.getMessage(), e);
            throw e;
        } finally {
            if (secret != null) {
                locker.unlock(lockKey, secret);
            }
        }

        if (awardAmount > 0) {
            notice(event, attr);
        }
    }

    public Map<String, String> buildHelpRiskExt(InviteTaskComponentAttr attr, long shareUid) {
        return ImmutableMap.of("actionType", Convert.toString(attr.getHelpRiskStrategyActionType()),
                "inviteUid", Convert.toString(shareUid));
    }


    private long saveInviteData(ActShareSuccessEvent event, InviteTaskComponentAttr attr) {
        //金币上限判断
        long awardedAmount = inviteTaskDao.getUserInviteAwardAmount(attr.getActId(), attr.getCmptUseInx(), event.getShareUid());
        boolean upperLimit = awardedAmount >= attr.getAwardLimit();

        //保存邀请记录
        String desc = upperLimit ? "金币达到上限" : "邀请成功";
        long awardAmount = upperLimit ? 0 : attr.getAwardAmount();
        int receiveState = upperLimit ? ReceiveState.REJECTED : ReceiveState.PENDING;
        int inviteState = upperLimit ? InviteState.OVER_LIMIT : InviteState.INVITED;
        int saveRet = inviteTaskDao.saveUserInvite(attr.getActId(), attr.getCmptUseInx(), event.getShareUid(), event.getUid(), event.getHdid()
                , desc, awardAmount, receiveState, inviteState);
        log.info("onActShareSuccessEvent saveUserInvite,actId:{},uid:{},inviteUid:{},saveRet:{}", attr.getActId(), event.getShareUid(), event.getUid(), saveRet);

        return awardAmount;
    }

    private void notice(ActShareSuccessEvent event, InviteTaskComponentAttr attr) {
        //邀请成功 IM消息提醒
        String nick = commonService.getNickName(event.getUid(), false);
        String imMsg = attr.getInviteSucceedMsg().replace("{nick}", nick);
        imMessageServiceClient.sendZhuiwanActIMPush(attr.getImMsgAppId(), attr.getImMsgSenderUid(),
                Lists.newArrayList(event.getShareUid()), attr.getInviteSucceedTitle(), imMsg, "", attr.getInviteSucceedLink());


        //被邀请人弹窗提醒
        String shareNick = URLEncoder.encode(commonService.getNickName(event.getShareUid(), true), StandardCharsets.UTF_8);
        AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
        appPopUpEvent.setUid(event.getUid());
        String url = attr.getReceiveInvitePopUrl().replace("{shareNick}", shareNick);
        appPopUpEvent.setPopUrl(url);
        appPopUpEvent.setSeq(makeKey(attr, "appSharePop_" + event.getSeq()));
        appPopUpEvent.setProductTime(System.currentTimeMillis());
        kafkaService.sendAppPopUp(appPopUpEvent);
    }


    private boolean effectEvent(ActShareSuccessEvent event, InviteTaskComponentAttr attr) {
        if (!attr.getApp().equals(event.getApp())) {
            log.info("not app,actId:{},uid:{},eventApp:{},configApp:{}", attr.getActId(), event.getUid(), event.getApp(), attr.getApp());
            return false;
        }
        if (event.getUid() <= 0) {
            log.info("uid empty,actId:{},shareUid:{}", attr.getActId(), event.getShareUid());
            return false;
        }
        if (event.getUid() == event.getShareUid()) {
            log.info("two users are the same person uid:{}", event.getUid());
            return false;
        }

        if (!StringUtil.isJson(event.getExtJson())) {
            log.info("ext is not json,seq:{},uid:{},ext:{}", event.getSeq(), event.getUid(), event.getExtJson());
            return false;
        }
        JSONObject extJson = JSON.parseObject(event.getExtJson());
        long eventActId = Convert.toLong(extJson.getLong("actId"), 0L);
        if (eventActId != attr.getActId()) {
            log.warn("not my actId,uid:{}", event.getUid());
            return false;
        }

        return true;
    }

    //TODO 待确认： 拿用户生成口令的时候的设备号，还是 接受邀请的时候，拿邀请人最近登录的设备号？
    private String queryShareLastHdid(long uid, String app) {
        var rsp = getLoginTime(uid, "", app, true);
        return rsp.getResult().getUidLastLoginDevice();
    }

    private void sendRiskRecheck(String app, long actId, long uid, String riskStrategyKey, int recheckCode, String msg, String title, String recordId, String param) {
        ActRiskReCheckEvent event = kafkaService.buildActRiskReCheckEvent(app, actId, uid, riskStrategyKey, recheckCode, msg, title, recordId, param);
        kafkaService.sendActRiskReCheckEvent(event);
    }

    private LoginRecord.LoginRecordRsp getLoginTime(long uid, String hdid, String app, boolean getUserLastHdidLoginTime) {
        ZhuiyaPbCommon.App appPb = null;
        if ("yomi".equals(app)) {
            appPb = ZhuiyaPbCommon.App.APP_YOMI;
        } else if ("zhuiwa".equals(app)) {
            appPb = ZhuiyaPbCommon.App.APP_ZHUIWAN;
        } else {
            throw new RuntimeException("not support app:" + app);
        }
        LoginRecord.LoginRecordReq req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setHdid(hdid)
                .setApp(appPb).build();
        if (StringUtil.isBlank(hdid) && getUserLastHdidLoginTime) {
            req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setUseLastHdid(true)
                    .setApp(appPb).build();
        }

        LoginRecord.LoginRecordRsp loginRecordRsp = loginClient.queryLoginRecord(req);
        log.info("isNewUser info @req:{},rsp:{}", JsonUtils.serialize(req), JsonUtils.serialize(loginRecordRsp));
        if (loginRecordRsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(500, "系统异常，请稍后重试");
        }
        return loginRecordRsp;
    }

    private List<InviteItem> queryCmpt5156UserInviteList(long actId, long cmptInx, long uid, Integer receiveState) {
        List<Cmpt5156UserInvite> invites = inviteTaskDao.getCmptUserInvite(actId, cmptInx, uid, receiveState);
        if (CollectionUtils.isEmpty(invites)) {
            return Lists.newArrayList();
        }

        List<Long> uids = invites.stream().map(Cmpt5156UserInvite::getInvitedUid).collect(Collectors.toList());
        Map<Long, UserInfoVo> userInfo = userInfoService.getUserInfo(uids, null);

        List<InviteItem> pendingList = Lists.newArrayList();
        for (Cmpt5156UserInvite invite : invites) {
            InviteItem item = new InviteItem();
            item.setCoin(invite.getAwardAmount());
            item.setId(invite.getId());
            item.setState(invite.getState());
            item.setReceiveState(invite.getAwardReceiveState());
            UserInfoVo userInfoVo = userInfo.get(invite.getInvitedUid());
            if (userInfoVo != null) {
                item.setNick(userInfoVo.getNick());
                item.setHeader(userInfoVo.getAvatarUrl());
            }
            pendingList.add(item);
        }

        return pendingList;
    }


    public void rlReport(long actId, long cmptInx) {
        Date now = new Date();
        Date preHour = DateUtil.addHours(now, -1);
        Date hourStatTime = DateUtil.getHourBeginTime(preHour);
        Date hourEndTime = DateUtil.getHourEndTime(preHour);

        //邀请成功参与用户数
        long userAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumUserAmount(actId, cmptInx, null, null, InviteState.INVITED), 0);

        //被邀请用户数
        long invitedAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumInvitedUserAmount(actId, cmptInx, null, null, InviteState.INVITED), 0);

        //邀请达上限用户数
        long userLimitAmount = Convert.toLong(cmpt5156UserInviteMapper.sumUserAmount(actId, cmptInx, null, null, InviteState.OVER_LIMIT), 0);

        //邀请达上限被邀请用户数
        long invitedLimitAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumInvitedUserAmount(actId, cmptInx, null, null, InviteState.OVER_LIMIT), 0);

        long receiveAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumAwardAmount(actId, cmptInx, null, null, ReceiveState.RECEIVED), 0);
        //未兑换金币数
        long notDrawAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumAwardAmount(actId, cmptInx, null, null, ReceiveState.PENDING), 0);

        //产生金币数
        long awardAmount = notDrawAmount + receiveAmount;


        String hourRange = DateUtil.format(hourStatTime) + "~" + DateUtil.format(hourEndTime);
        //小时邀请成功参与用户数
        long hourUserAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumUserAmount(actId, cmptInx, hourStatTime, hourEndTime, InviteState.INVITED), 0);

        //小时被邀请用户数
        long hourInvitedAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumInvitedUserAmount(actId, cmptInx, hourStatTime, hourEndTime, InviteState.INVITED), 0);

        //小时邀请达上限用户数
        long hourUserLimitAmount = Convert.toLong(cmpt5156UserInviteMapper.sumUserAmount(actId, cmptInx, hourStatTime, hourEndTime, InviteState.OVER_LIMIT), 0);

        //小时邀请达上限被邀请用户数
        long hourInvitedLimitAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumInvitedUserAmount(actId, cmptInx, hourStatTime, hourEndTime, InviteState.OVER_LIMIT), 0);

        long hourReceiveAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumAwardAmount(actId, cmptInx, hourStatTime, hourEndTime, ReceiveState.RECEIVED), 0);
        //小时未兑换金币数
        long hourNotDrawAmount
                = Convert.toLong(cmpt5156UserInviteMapper.sumAwardAmount(actId, cmptInx, hourStatTime, hourEndTime, ReceiveState.PENDING), 0);

        //小时产生金币数
        long hourAwardAmount = hourNotDrawAmount + hourReceiveAmount;

        String msg = REPORT_TEMPLATE
                .replace("${userAmount}", userAmount + "")
                .replace("${invitedAmount}", invitedAmount + "")
                .replace("${userLimitAmount}", userLimitAmount + "")
                .replace("${invitedLimitAmount}", invitedLimitAmount + "")
                .replace("${awardAmount}", awardAmount + "")
                .replace("${notDrawAmount}", notDrawAmount + "")
                .replace("${hourRange}", hourRange)
                .replace("${hourUserAmount}", hourUserAmount + "")
                .replace("${hourInvitedAmount}", hourInvitedAmount + "")
                .replace("${hourUserLimitAmount}", hourUserLimitAmount + "")
                .replace("${hourInvitedLimitAmount}", hourInvitedLimitAmount + "")
                .replace("${hourInvitedLimitAmount}", hourAwardAmount + "")
                .replace("${hourAwardAmount}", hourAwardAmount + "")
                .replace("${hourNotDrawAmount}", hourNotDrawAmount + "");
        String ruliuMsg = buildActRuliuMsg(actId, false, "活动数据跟踪", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, ruliuMsg, Collections.emptyList());

    }

    /**
     * 顶部提醒
     */
    public void doSendNotice(InviteTaskComponentAttr attr, long uid, String message) {
        String seq = UUID.randomUUID().toString();
        Map<String, String> extend = Maps.newHashMapWithExpectedSize(2);
        //客户端不用等待首页弹窗完成也能显示
        extend.put("ignoreMainPopup", "1");

        YoPopupMessage yoMessage = YoPopupMessage.builder()
                .app("yomi")
                //默认android,-1 安卓+ios
                .platform(-1)
                .title(attr.getInviteFailedTitle())
                .content(Base64Utils.encodeToString(message.getBytes()))
                .innerContent(message)
                .icon(attr.getInviteFailedIcon())
                .extend(extend)
                .link("").build();
        zhuiWanPrizeIssueServiceClient.sendPopupMessage(seq, uid, yoMessage);
    }

    private List<String> queryInviteLogList(Long actId, Long uid, Long invitedUid, Integer limitPara) {
        long uidValue = Convert.toLong(uid, 0);
        long inviteUidValue = Convert.toLong(invitedUid, 0);
        if (uidValue == 0 && inviteUidValue == 0) {
            return Lists.newArrayList("uid或invitedUid不能为空");
        }
        int limit = limitPara == null ? 20 : Math.min(limitPara, 100);
        List<Cmpt5156UserInviteLog> logs = inviteTaskDao.queryInviteLog(actId, uidValue, inviteUidValue, limit);
        if (CollectionUtils.isEmpty(logs)) {
            return Lists.newArrayList("暂无数据");
        }

        StringBuilder inviteContent = new StringBuilder("邀请人uid | 被邀请人uid | 时间 | 邀请拦截日志");
        for (Cmpt5156UserInviteLog log : logs) {
            inviteContent.append(String.format("%s | %s | %s | %s \n "
                    , log.getUid(), log.getInvitedUid(), DateUtil.format(log.getCreateTime()), log.getInvitedDesc()));
        }
        return Lists.newArrayList(inviteContent.toString());
    }


    private static class InviteFailedState {
        /**
         * 风控
         */
        public static final int RISK = 1;
        /**
         * 二次挑战
         */
        public static final int RISK_RECHECK = 2;
        /**
         * 老uid
         */
        public static final int OLD_USER = 3;
        /**
         * 老设备
         */
        public static final int OLD_HDID = 4;
        /**
         * uid被其他人邀请过
         */
        private static final int DUP_UID = 5;
        /**
         * 设备号被其他人邀请过
         */
        private static final int DUP_HDID = 6;

        /**
         * 手机号码未绑定
         */
        private static final int MOBILE_NOT_BIND = 7;
        /**
         * 邀请人 uid-手机号-设备唯一性 变化
         */
        private static final int BIND_INFO_CHANGE = 8;
        /**
         * 邀请人获得金币超出上限
         */
        private static final int LIMIT = 9;
    }

    private static class ReceiveState {
        /**
         * 0==待领取
         */
        public static final int PENDING = 0;
        /**
         * 1==已领取
         */
        public static final int RECEIVED = 1;
        /**
         * -1==无需领取
         */
        public static final int REJECTED = -1;
    }

    private static class InviteState {
        /**
         * 100==邀请成功
         */
        public static final int INVITED = 100;
        /**
         * 200==金币达到上限
         */
        public static final int OVER_LIMIT = 200;
    }

    @Data
    public static class PendingCoinRsp {
        private List<InviteItem> pendingList;
    }

    @Data
    public static class InviteItem {
        private long id;
        private String nick;
        private String header;
        private long coin;

        private int state;
        private int receiveState;
    }

    @Data
    public static class InviteListRsp {
        private List<InviteItem> inviteList;
    }

    @Data
    public static class UserStateRsp {
        private int invited;
        private long balance;
        private String header;
    }


}
