package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022.10.12 20:49
 */
@Data
public class KnockoutAnchorTransferComponentAttr extends ComponentAttr {
    /**
     * 需要监听的榜单分值改变
     **/
    @ComponentAttrField(labelText = "榜单id", remark = "需要监听的榜单分值改变,多个时逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class, labelText = "榜单id")
    })
    private List<Long> scoreRankId;

    /**
     * 记录事件的榜单ID,仅用于中控,将多个榜单的事件汇集在一起
     **/
    @ComponentAttrField(labelText = "记录事件的榜单ID", remark = "记录事件的榜单ID,仅用于中控,将多个榜单的事件汇集在一起")
    private long hdzkRankId;

    /**
     * 淘汰主播角色转换
     **/
    @ComponentAttrField(labelText = "淘汰主播角色转换", subFields = {
            @SubField(fieldName = Constant.KEY1, labelText = "旧角色", type = Long.class),
            @SubField(fieldName = Constant.VALUE, labelText = "新角色", type = Long.class)
    })
    private Map<Long, Long> roleTransferMap;
    /**
     * 业务id
     **/
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    /**
     * 阶段包含的所有需要监听的榜单ID
     **/
    @ComponentAttrField(labelText = "阶段包含的所有需要监听的榜单ID", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "阶段id"),
            @SubField(fieldName = Constant.VALUE, type = List.class, skip = true),
            @SubField(fieldName = Constant.MAP_LIST_VALUE, type = Long.class, labelText = "榜单id", remark = "多个时逗号分隔", propType = ComponentAttrCollector.PropType.TEXT)
    })
    private Map<Long, List<Long>> phaseContainRankId;

    /**
     * 角色类型 200 -anchor 400-> channel
     **/
    @ComponentAttrField(labelText = "角色类型", dropDownSourceBeanClass = RoleTypeSource.class)
    private long roleType;

    /**
     * 开始记录榜单事件的阶段ID,小于这个ID不记录,解决第一阶段累榜问题
     **/
    @ComponentAttrField(labelText = "开始记录榜单事件的阶段ID", remark = "小于这个ID不记录,解决第一阶段累榜问题")
    private long startSavePhaseId;

    /**
     * 上报数据的giftId
     **/
    @ComponentAttrField(labelText = "上报数据的giftId")
    private String zkGiftId;
}
