package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.mq.SendGiftEvent;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.WechatRobotService;
import com.yy.gameecology.common.bean.WechatRobotMsg;
import com.yy.gameecology.common.consts.BusiId;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SummerAdventureComponentAttr;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 夏日探险玩法组件
 * 
 * 功能说明：
 * 1. CP通过送礼获得骰子
 * 2. 双人确认后掷骰子在地图上前进
 * 3. 根据停留格子获得奖励
 * 4. 支持黑名单过滤和统计推送
 */
@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/cmpt/summerAdventure")
@Component
public class SummerAdventureComponent extends BaseActComponent<SummerAdventureComponentAttr> {

    // Redis Key 定义
    private static final String CP_DICE_COUNT = "summer_adventure_dice_count:%s"; // CP骰子数量
    private static final String CP_POSITION = "summer_adventure_position:%s"; // CP当前位置
    private static final String CP_LATEST = "summer_adventure_latest_cp:%d"; // 主持最新CP
    private static final String DICE_STATISTICS = "summer_adventure_dice_stats"; // 骰子统计
    private static final String REWARD_STATISTICS = "summer_adventure_reward_stats"; // 奖励统计
    private static final String PARTICIPATION_COUNT = "summer_adventure_participation"; // 参与次数统计
    private static final String REWARD_RECORDS = "summer_adventure_reward_records"; // 中奖记录
    private static final String GLOBAL_DICE_COUNT = "summer_adventure_global_dice"; // 全服骰子累计数
    private static final String REWARD_STOCK = "summer_adventure_reward_stock"; // 奖励库存

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private WechatRobotService wechatRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.SUMMER_ADVENTURE;
    }

    /**
     * 处理送礼事件，计算骰子获得
     */
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void handleSendGift(SendGiftEvent event, SummerAdventureComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        // 检查业务ID
        BusiId eventBusiId = broadCastHelpService.changeBroTemplate2BusiId(event.getTemplate());
        if (attr.getBusiId() != eventBusiId.getValue()) {
            return;
        }

        long sendUid = event.getSendUid();
        long recvUid = event.getRecvUid();
        String giftId = event.getGiftId();
        long giftNum = event.getGiftNum();

        // 检查是否为活动礼物
        double giftValue = getGiftValue(attr, giftId);
        if (giftValue <= 0) {
            return;
        }

        // 检查黑名单
        if (attr.isEnableBlacklistCheck() && isInBlacklist(event)) {
            log.info("CP in blacklist, skip dice calculation. sendUid:{}, recvUid:{}", sendUid, recvUid);
            return;
        }

        // 计算骰子数量
        double totalValue = giftValue * giftNum;
        int diceCount = (int) (totalValue / attr.getDiceThreshold());
        
        if (diceCount > 0) {
            String cpKey = buildCpKey(sendUid, recvUid);
            addDiceForCp(attr, cpKey, diceCount, sendUid, recvUid);
            
            // 更新最新CP
            updateLatestCp(attr, recvUid, sendUid);
            
            // 更新全服骰子统计
            updateGlobalDiceCount(attr, diceCount);
            
            log.info("CP获得骰子: cpKey:{}, diceCount:{}, totalValue:{}", cpKey, diceCount, totalValue);
        }
    }

    /**
     * 获取玩法信息
     */
    @GetMapping("/getGameInfo")
    public Response<GameInfo> getGameInfo(long uid) {
        SummerAdventureComponentAttr attr = getAttr();
        if (attr == null) {
            return Response.fail("组件未配置");
        }

        GameInfo gameInfo = new GameInfo();
        
        // 获取用户的CP信息
        List<CpInfo> cpInfos = getUserCpInfos(attr, uid);
        gameInfo.setCpInfos(cpInfos);
        
        // 获取地图配置
        gameInfo.setMapRewards(attr.getMapRewards());
        
        // 获取统计信息
        gameInfo.setStatistics(getGameStatistics(attr));
        
        return Response.success(gameInfo);
    }

    /**
     * 掷骰子操作
     */
    @PostMapping("/rollDice")
    public Response<RollResult> rollDice(long uid, String cpKey, boolean confirmed) {
        SummerAdventureComponentAttr attr = getAttr();
        if (attr == null) {
            return Response.fail("组件未配置");
        }

        if (!actInfoService.inActTime(attr.getActId())) {
            return Response.fail("不在活动时间内");
        }

        // 检查CP是否有效
        if (!isValidCp(cpKey, uid)) {
            return Response.fail("无效的CP关系");
        }

        // 获取当前骰子数量
        int currentDice = getCurrentDiceCount(attr, cpKey);
        if (currentDice <= 0) {
            return Response.fail("骰子不足，一次性赠送131.4元礼物可获骰子哦~");
        }

        // 确认机制处理
        if (!confirmed) {
            return Response.success(RollResult.builder()
                    .needConfirm(true)
                    .currentDice(currentDice)
                    .willConsume(Math.min(currentDice, attr.getMaxDicePerRound()))
                    .build());
        }

        // 执行掷骰子
        return executeRollDice(attr, cpKey, currentDice);
    }

    /**
     * 获取中奖记录
     */
    @GetMapping("/getRewardRecords")
    public Response<RewardRecords> getRewardRecords(long uid, int type, int page, int size) {
        SummerAdventureComponentAttr attr = getAttr();
        if (attr == null) {
            return Response.fail("组件未配置");
        }

        RewardRecords records = new RewardRecords();
        
        if (type == 1) {
            // 我的中奖记录
            records.setRecords(getMyRewardRecords(attr, uid, page, size));
        } else {
            // 全服中奖记录
            records.setRecords(getGlobalRewardRecords(attr, page, size));
        }
        
        return Response.success(records);
    }

    // ==================== 私有方法 ====================

    /**
     * 获取礼物价值
     */
    private double getGiftValue(SummerAdventureComponentAttr attr, String giftId) {
        return attr.getGiftConfigs().stream()
                .filter(config -> config.getGiftId().equals(giftId))
                .mapToDouble(SummerAdventureComponentAttr.GiftConfig::getValue)
                .findFirst()
                .orElse(0.0);
    }

    /**
     * 检查是否在黑名单中
     */
    private boolean isInBlacklist(SendGiftEvent event) {
        // TODO: 接入追吖管理系统黑名单检查
        // 这里需要根据实际的黑名单系统接口来实现
        return false;
    }

    /**
     * 构建CP键
     */
    private String buildCpKey(long uid1, long uid2) {
        // 确保CP键的唯一性，较小的uid在前
        if (uid1 < uid2) {
            return uid1 + "_" + uid2;
        } else {
            return uid2 + "_" + uid1;
        }
    }

    /**
     * 为CP添加骰子
     */
    private void addDiceForCp(SummerAdventureComponentAttr attr, String cpKey, int diceCount, long sendUid, long recvUid) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String diceKey = String.format(CP_DICE_COUNT, cpKey);
        
        // 检查当前骰子数量
        int currentDice = actRedisDao.hGetInt(redisGroup, diceKey, "count", 0);
        int newDiceCount = Math.min(currentDice + diceCount, attr.getMaxDiceCount());
        
        if (newDiceCount > currentDice) {
            actRedisDao.hset(redisGroup, diceKey, "count", String.valueOf(newDiceCount));
            actRedisDao.hset(redisGroup, diceKey, "sendUid", String.valueOf(sendUid));
            actRedisDao.hset(redisGroup, diceKey, "recvUid", String.valueOf(recvUid));
            actRedisDao.hset(redisGroup, diceKey, "updateTime", String.valueOf(System.currentTimeMillis()));
            
            // 如果是首次获得骰子，发送引导提示
            if (currentDice == 0) {
                sendFirstDiceGuide(attr, sendUid, recvUid);
            }
        }
    }

    /**
     * 执行掷骰子操作
     */
    private Response<RollResult> executeRollDice(SummerAdventureComponentAttr attr, String cpKey, int currentDice) {
        String redisGroup = getRedisGroupCode(attr.getActId());

        // 计算消耗的骰子数量
        int consumeDice = Math.min(currentDice, attr.getMaxDicePerRound());

        // 扣除骰子
        String diceKey = String.format(CP_DICE_COUNT, cpKey);
        int remainingDice = currentDice - consumeDice;
        actRedisDao.hset(redisGroup, diceKey, "count", String.valueOf(remainingDice));

        // 获取当前位置
        String positionKey = String.format(CP_POSITION, cpKey);
        int currentPosition = actRedisDao.hGetInt(redisGroup, positionKey, "position", 1);

        // 掷骰子并计算结果
        List<DiceResult> diceResults = Lists.newArrayList();
        List<RewardInfo> rewards = Lists.newArrayList();
        int finalPosition = currentPosition;

        for (int i = 0; i < consumeDice; i++) {
            int points = ThreadLocalRandom.current().nextInt(1, 7); // 1-6点
            finalPosition = calculateNewPosition(finalPosition, points);

            DiceResult diceResult = new DiceResult();
            diceResult.setPoints(points);
            diceResult.setNewPosition(finalPosition);
            diceResults.add(diceResult);

            // 检查是否有奖励
            SummerAdventureComponentAttr.MapReward reward = getRewardByPosition(attr, finalPosition);
            if (reward != null && !"无".equals(reward.getRewardType())) {
                RewardInfo rewardInfo = new RewardInfo();
                rewardInfo.setRewardName(reward.getRewardName());
                rewardInfo.setRewardIcon(reward.getRewardIcon());
                rewardInfo.setPosition(finalPosition);
                rewards.add(rewardInfo);

                // 发放奖励
                issueReward(attr, cpKey, reward);
            }
        }

        // 更新位置
        actRedisDao.hset(redisGroup, positionKey, "position", String.valueOf(finalPosition));

        // 更新统计
        updateParticipationStats(attr);
        actRedisDao.hIncrByKey(redisGroup, DICE_STATISTICS, "totalUsed", consumeDice);

        RollResult result = new RollResult();
        result.setNeedConfirm(false);
        result.setCurrentDice(remainingDice);
        result.setWillConsume(consumeDice);
        result.setDiceResults(diceResults);
        result.setRewards(rewards);
        result.setFinalPosition(finalPosition);

        return Response.success(result);
    }

    /**
     * 计算新位置（循环地图）
     */
    private int calculateNewPosition(int currentPosition, int points) {
        int newPosition = currentPosition + points;
        if (newPosition > 30) {
            newPosition = newPosition - 30;
        }
        return newPosition;
    }

    /**
     * 根据位置获取奖励配置
     */
    private SummerAdventureComponentAttr.MapReward getRewardByPosition(SummerAdventureComponentAttr attr, int position) {
        return attr.getMapRewards().stream()
                .filter(reward -> reward.getPosition() == position)
                .findFirst()
                .orElse(null);
    }

    /**
     * 发放奖励
     */
    private void issueReward(SummerAdventureComponentAttr attr, String cpKey, SummerAdventureComponentAttr.MapReward reward) {
        try {
            // 检查库存
            if (!checkAndConsumeStock(attr, reward)) {
                log.warn("奖励库存不足: position:{}, rewardName:{}", reward.getPosition(), reward.getRewardName());
                return;
            }

            // 获取CP信息
            String[] parts = cpKey.split("_");
            long uid1 = Long.parseLong(parts[0]);
            long uid2 = Long.parseLong(parts[1]);

            // 调用发奖接口
            String seq = UUID.randomUUID().toString();
            hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(),
                    uid1, attr.getWelfareTaskId(), reward.getPackageId(), seq);
            hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(),
                    uid2, attr.getWelfareTaskId(), reward.getPackageId(), seq + "_2");

            // 记录中奖记录
            recordReward(attr, uid1, uid2, reward);

            // 检查是否需要全服广播
            if (isHighValueReward(attr, reward)) {
                broadcastReward(attr, uid1, uid2, reward);
            }

            log.info("奖励发放成功: cpKey:{}, reward:{}", cpKey, reward.getRewardName());

        } catch (Exception e) {
            log.error("奖励发放失败: cpKey:{}, reward:{}, error:{}", cpKey, reward.getRewardName(), e.getMessage(), e);
        }
    }

    /**
     * 检查并消耗库存
     */
    private boolean checkAndConsumeStock(SummerAdventureComponentAttr attr, SummerAdventureComponentAttr.MapReward reward) {
        if (reward.getStockRule() == null || reward.getStockRule().isEmpty()) {
            return true; // 无库存限制
        }

        String redisGroup = getRedisGroupCode(attr.getActId());
        String stockKey = REWARD_STOCK + ":" + reward.getPosition();

        // 获取当前库存
        int currentStock = actRedisDao.hGetInt(redisGroup, stockKey, "stock", 0);
        if (currentStock <= 0) {
            return false;
        }

        // 扣除库存
        actRedisDao.hIncrByKey(redisGroup, stockKey, "stock", -1);
        return true;
    }

    /**
     * 更新奖励库存（基于全服骰子数量）
     */
    private void updateRewardStock(SummerAdventureComponentAttr attr, int diceCount) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        long totalDice = actRedisDao.hGetLong(redisGroup, GLOBAL_DICE_COUNT, "total", 0L);

        for (SummerAdventureComponentAttr.MapReward reward : attr.getMapRewards()) {
            if (reward.getStockRule() != null && !reward.getStockRule().isEmpty()) {
                updateSingleRewardStock(attr, reward, totalDice);
            }
        }
    }

    /**
     * 更新单个奖励的库存
     */
    private void updateSingleRewardStock(SummerAdventureComponentAttr attr, SummerAdventureComponentAttr.MapReward reward, long totalDice) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String stockKey = REWARD_STOCK + ":" + reward.getPosition();

        // 根据库存规则计算应有库存
        int shouldHaveStock = calculateStockByRule(reward.getStockRule(), reward.getStockThreshold(), totalDice);
        int currentStock = actRedisDao.hGetInt(redisGroup, stockKey, "stock", 0);

        if (shouldHaveStock > currentStock) {
            actRedisDao.hset(redisGroup, stockKey, "stock", String.valueOf(shouldHaveStock));
        }
    }

    /**
     * 根据规则计算库存
     */
    private int calculateStockByRule(String rule, int threshold, long totalDice) {
        // 解析规则，例如："每累计生成16个骰子，库存+1"
        if (rule.contains("每累计生成") && rule.contains("个骰子")) {
            return (int) (totalDice / threshold);
        }
        return 0;
    }

    /**
     * 判断是否为高价值奖励（需要广播）
     */
    private boolean isHighValueReward(SummerAdventureComponentAttr attr, SummerAdventureComponentAttr.MapReward reward) {
        // 根据配置的最小广播价值判断
        // 这里简化处理，实际应该根据奖励的实际价值判断
        return reward.getRewardName().contains("小憩一夏") ||
               reward.getRewardName().contains("缤纷启程入场秀");
    }

    /**
     * 全服广播中奖信息
     */
    private void broadcastReward(SummerAdventureComponentAttr attr, long uid1, long uid2, SummerAdventureComponentAttr.MapReward reward) {
        try {
            // TODO: 实现全服广播逻辑
            log.info("全服广播中奖: uid1:{}, uid2:{}, reward:{}", uid1, uid2, reward.getRewardName());
        } catch (Exception e) {
            log.error("广播失败: uid1:{}, uid2:{}, reward:{}, error:{}", uid1, uid2, reward.getRewardName(), e.getMessage(), e);
        }
    }

    /**
     * 记录中奖记录
     */
    private void recordReward(SummerAdventureComponentAttr attr, long uid1, long uid2, SummerAdventureComponentAttr.MapReward reward) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        long timestamp = System.currentTimeMillis();

        // 记录全服中奖记录
        RewardRecord record1 = new RewardRecord();
        record1.setUid(uid1);
        record1.setNick("用户" + uid1);
        record1.setAvatar("");
        record1.setRewardName(reward.getRewardName());
        record1.setTimestamp(timestamp);
        record1.setHighValue(isHighValueReward(attr, reward));

        RewardRecord record2 = new RewardRecord();
        record2.setUid(uid2);
        record2.setNick("用户" + uid2);
        record2.setAvatar("");
        record2.setRewardName(reward.getRewardName());
        record2.setTimestamp(timestamp);
        record2.setHighValue(isHighValueReward(attr, reward));

        actRedisDao.lpush(redisGroup, REWARD_RECORDS, JSON.toJSONString(record1));
        actRedisDao.lpush(redisGroup, REWARD_RECORDS, JSON.toJSONString(record2));

        // 保持记录数量在合理范围内
        actRedisDao.ltrim(redisGroup, REWARD_RECORDS, 0, 999);
    }

    /**
     * 更新参与统计
     */
    private void updateParticipationStats(SummerAdventureComponentAttr attr) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String today = DateUtil.formatDate(new Date(), "yyyyMMdd");

        actRedisDao.hIncrByKey(redisGroup, PARTICIPATION_COUNT, today, 1);
        actRedisDao.hIncrByKey(redisGroup, PARTICIPATION_COUNT, "total", 1);
    }

    /**
     * 获取我的中奖记录
     */
    private List<RewardRecord> getMyRewardRecords(SummerAdventureComponentAttr attr, long uid, int page, int size) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        List<String> records = actRedisDao.lrange(redisGroup, REWARD_RECORDS, 0, -1);

        return records.stream()
                .map(record -> JSON.parseObject(record, RewardRecord.class))
                .filter(record -> record.getUid() == uid)
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }

    /**
     * 获取全服中奖记录
     */
    private List<RewardRecord> getGlobalRewardRecords(SummerAdventureComponentAttr attr, int page, int size) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        List<String> records = actRedisDao.lrange(redisGroup, REWARD_RECORDS, page * size, (page + 1) * size - 1);

        return records.stream()
                .map(record -> JSON.parseObject(record, RewardRecord.class))
                .collect(Collectors.toList());
    }

    /**
     * 获取用户的CP信息列表
     */
    private List<CpInfo> getUserCpInfos(SummerAdventureComponentAttr attr, long uid) {
        List<CpInfo> cpInfos = Lists.newArrayList();
        String redisGroup = getRedisGroupCode(attr.getActId());

        // 查找用户作为主持的最新CP
        String latestCpKey = String.format(CP_LATEST, uid);
        String latestCpUid = actRedisDao.hget(redisGroup, latestCpKey, "cpUid");

        if (latestCpUid != null) {
            String cpKey = buildCpKey(uid, Long.parseLong(latestCpUid));
            CpInfo cpInfo = buildCpInfo(attr, cpKey, uid, Long.parseLong(latestCpUid));
            if (cpInfo != null) {
                cpInfos.add(cpInfo);
            }
        }

        return cpInfos;
    }

    /**
     * 构建CP信息
     */
    private CpInfo buildCpInfo(SummerAdventureComponentAttr attr, String cpKey, long uid1, long uid2) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String diceKey = String.format(CP_DICE_COUNT, cpKey);
        String positionKey = String.format(CP_POSITION, cpKey);

        Map<String, String> diceInfo = actRedisDao.hgetAll(redisGroup, diceKey);
        if (diceInfo.isEmpty()) {
            return null;
        }

        CpInfo cpInfo = new CpInfo();
        cpInfo.setCpKey(cpKey);
        cpInfo.setSendUid(uid1);
        cpInfo.setRecvUid(uid2);
        cpInfo.setDiceCount(Integer.parseInt(diceInfo.getOrDefault("count", "0")));
        cpInfo.setCurrentPosition(actRedisDao.hGetInt(redisGroup, positionKey, "position", 1));

        // TODO: 获取用户昵称和头像信息
        cpInfo.setSendNick("用户" + uid1);
        cpInfo.setRecvNick("用户" + uid2);
        cpInfo.setSendAvatar("");
        cpInfo.setRecvAvatar("");

        return cpInfo;
    }

    /**
     * 获取游戏统计信息
     */
    private GameStatistics getGameStatistics(SummerAdventureComponentAttr attr) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        GameStatistics statistics = new GameStatistics();

        // 获取骰子统计
        Map<String, String> diceStats = actRedisDao.hgetAll(redisGroup, DICE_STATISTICS);
        statistics.setTotalDiceGenerated(Long.parseLong(diceStats.getOrDefault("totalGenerated", "0")));
        statistics.setTotalDiceUsed(Long.parseLong(diceStats.getOrDefault("totalUsed", "0")));

        // 获取参与统计
        String today = DateUtil.formatDate(new Date(), "yyyyMMdd");
        statistics.setTodayParticipation(actRedisDao.hGetLong(redisGroup, PARTICIPATION_COUNT, today, 0L));
        statistics.setTotalParticipation(actRedisDao.hGetLong(redisGroup, PARTICIPATION_COUNT, "total", 0L));

        // 获取奖励统计
        Map<String, RewardStat> rewardStats = Maps.newHashMap();
        Map<String, String> rewardStatsData = actRedisDao.hgetAll(redisGroup, REWARD_STATISTICS);
        for (Map.Entry<String, String> entry : rewardStatsData.entrySet()) {
            RewardStat stat = JSON.parseObject(entry.getValue(), RewardStat.class);
            rewardStats.put(entry.getKey(), stat);
        }
        statistics.setRewardStats(rewardStats);

        return statistics;
    }

    /**
     * 检查CP是否有效
     */
    private boolean isValidCp(String cpKey, long uid) {
        String[] parts = cpKey.split("_");
        if (parts.length != 2) {
            return false;
        }

        try {
            long uid1 = Long.parseLong(parts[0]);
            long uid2 = Long.parseLong(parts[1]);
            return uid == uid1 || uid == uid2;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取当前骰子数量
     */
    private int getCurrentDiceCount(SummerAdventureComponentAttr attr, String cpKey) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String diceKey = String.format(CP_DICE_COUNT, cpKey);
        return actRedisDao.hGetInt(redisGroup, diceKey, "count", 0);
    }

    /**
     * 更新最新CP关系
     */
    private void updateLatestCp(SummerAdventureComponentAttr attr, long recvUid, long sendUid) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String latestKey = String.format(CP_LATEST, recvUid);
        actRedisDao.hset(redisGroup, latestKey, "cpUid", String.valueOf(sendUid));
        actRedisDao.hset(redisGroup, latestKey, "updateTime", String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 更新全服骰子统计
     */
    private void updateGlobalDiceCount(SummerAdventureComponentAttr attr, int diceCount) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        actRedisDao.hIncrByKey(redisGroup, DICE_STATISTICS, "totalGenerated", diceCount);
        actRedisDao.hIncrByKey(redisGroup, GLOBAL_DICE_COUNT, "total", diceCount);

        // 更新奖励库存
        updateRewardStock(attr, diceCount);
    }

    /**
     * 发送首次获得骰子引导
     */
    private void sendFirstDiceGuide(SummerAdventureComponentAttr attr, long sendUid, long recvUid) {
        // TODO: 实现首次获得骰子的引导提示
        log.info("首次获得骰子引导: sendUid:{}, recvUid:{}", sendUid, recvUid);
    }

    // ==================== 数据类定义 ====================

    @Data
    public static class GameInfo {
        private List<CpInfo> cpInfos;
        private List<SummerAdventureComponentAttr.MapReward> mapRewards;
        private GameStatistics statistics;
    }

    @Data
    public static class CpInfo {
        private String cpKey;
        private long sendUid;
        private long recvUid;
        private String sendNick;
        private String recvNick;
        private String sendAvatar;
        private String recvAvatar;
        private int diceCount;
        private int currentPosition;
    }

    @Data
    public static class GameStatistics {
        private long totalDiceGenerated;
        private long totalDiceUsed;
        private long todayParticipation;
        private long totalParticipation;
        private Map<String, RewardStat> rewardStats;
    }

    @Data
    public static class RewardStat {
        private long totalCount;
        private double probability;
        private long totalValue;
        private int currentStock;
    }

    @Data
    public static class RollResult {
        private boolean needConfirm;
        private int currentDice;
        private int willConsume;
        private List<DiceResult> diceResults;
        private List<RewardInfo> rewards;
        private int finalPosition;

        public static RollResultBuilder builder() {
            return new RollResultBuilder();
        }

        public static class RollResultBuilder {
            private RollResult result = new RollResult();

            public RollResultBuilder needConfirm(boolean needConfirm) {
                result.needConfirm = needConfirm;
                return this;
            }

            public RollResultBuilder currentDice(int currentDice) {
                result.currentDice = currentDice;
                return this;
            }

            public RollResultBuilder willConsume(int willConsume) {
                result.willConsume = willConsume;
                return this;
            }

            public RollResult build() {
                return result;
            }
        }
    }

    @Data
    public static class DiceResult {
        private int points;
        private int newPosition;
    }

    @Data
    public static class RewardInfo {
        private String rewardName;
        private String rewardIcon;
        private int position;
    }

    @Data
    public static class RewardRecords {
        private List<RewardRecord> records;
        private int total;
    }

    @Data
    public static class RewardRecord {
        private long uid;
        private String nick;
        private String avatar;
        private String rewardName;
        private long timestamp;
        private boolean isHighValue;
    }
}
}
